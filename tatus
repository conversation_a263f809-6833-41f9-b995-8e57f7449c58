[33mf5fe0c7[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32mmain[m[33m, [m[1;31morigin/main[m[33m, [m[1;31morigin/HEAD[m[33m)[m feat: completed patients_portal dashboard, refined frontend design, cleaned up database
[33m29184ef[m fix: refined backend for patients portal.
[33m4ca8d7c[m feat: implemented patients readonly portal
[33m5eedbc1[m feat: added qrcode scan functionality for creating prescriptions
[33mf4d9f28[m Merge pull request #10 from alenstein/fix/prescriptions-delete-refresh
