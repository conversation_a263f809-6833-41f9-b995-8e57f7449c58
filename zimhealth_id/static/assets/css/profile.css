/* Professional Profile Page Styles for ZimHealth-ID */

/* Government-grade profile layout */
.profile-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    position: relative;
}

/* Sophisticated background pattern */
.profile-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.02) 0%, transparent 50%);
    background-size: 1200px 1200px, 800px 800px;
    pointer-events: none;
    z-index: 0;
}

/* Enhanced profile header styling */
.profile-header {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.2);
    position: relative;
    z-index: 10;
}

/* Professional Profile Card - Patients Page Design Language */
.profile-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.profile-card:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-2px);
}

/* Enhanced profile avatar - Compact and Non-Obstructive */
.profile-avatar-container {
    position: relative;
    display: inline-block;
}

.profile-avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.9);
    box-shadow:
        0 4px 15px rgba(14, 165, 233, 0.15),
        0 2px 8px rgba(0, 0, 0, 0.1);
    object-fit: cover;
    transition: all 0.3s ease;
}

.profile-avatar-placeholder {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.9);
    background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow:
        0 4px 15px rgba(14, 165, 233, 0.15),
        0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.profile-avatar-placeholder i {
    color: #6b7280;
    font-size: 1.5rem;
}

/* Compact status badges */
.profile-status-badge {
    padding: 0.5rem 1rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.75rem;
    display: inline-flex;
    align-items: center;
    border: 1px solid;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.25px;
}

.profile-status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.profile-status-badge:hover::before {
    left: 100%;
}

.profile-status-badge.verified {
    background: linear-gradient(135deg,
        rgba(20, 184, 166, 0.2),
        rgba(6, 182, 212, 0.15),
        rgba(14, 165, 233, 0.1));
    border-color: rgba(20, 184, 166, 0.4);
    color: #0f766e;
    box-shadow:
        0 4px 12px rgba(20, 184, 166, 0.25),
        0 2px 6px rgba(20, 184, 166, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.profile-status-badge.verified:hover {
    transform: translateY(-1px) scale(1.02);
    box-shadow:
        0 6px 16px rgba(20, 184, 166, 0.3),
        0 3px 8px rgba(20, 184, 166, 0.2);
}

.profile-status-badge.unverified {
    background: linear-gradient(135deg,
        rgba(239, 68, 68, 0.2),
        rgba(220, 38, 38, 0.15),
        rgba(185, 28, 28, 0.1));
    border-color: rgba(239, 68, 68, 0.4);
    color: #b91c1c;
    box-shadow:
        0 4px 12px rgba(239, 68, 68, 0.25),
        0 2px 6px rgba(239, 68, 68, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.profile-status-badge.unverified:hover {
    transform: translateY(-1px) scale(1.02);
    box-shadow:
        0 6px 16px rgba(239, 68, 68, 0.3),
        0 3px 8px rgba(239, 68, 68, 0.2);
}

/* Professional Proper Avatar Design */
.profile-avatar-container-proper {
    position: relative;
    display: inline-block;
}

/* Enhanced Profile Avatar - Compact Size */
.profile-avatar-proper {
    width: 96px;
    height: 96px;
    border-radius: 50%;
    border: 3px solid #ffffff;
    box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.1), 0 3px 5px -1px rgba(0, 0, 0, 0.05);
    object-fit: cover;
    transition: all 0.3s ease;
    position: relative;
    z-index: 10;
    background: #e5e7eb;
}

.profile-avatar-placeholder-proper {
    width: 96px;
    height: 96px;
    border-radius: 50%;
    border: 3px solid #ffffff;
    background: #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.1), 0 3px 5px -1px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    z-index: 10;
}

.profile-avatar-placeholder-proper i {
    color: #6b7280;
    font-size: 2.25rem;
}



/* Government-Level Professional Form Styling */
.profile-form-input {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    color: #374151;
    transition: all 0.2s ease;
    width: 100%;
}

.profile-form-input:focus {
    outline: none;
    border-color: #1e40af;
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
    background: #fafbff;
}

.profile-form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.profile-form-label i {
    color: #1e40af;
    margin-right: 0.5rem;
}

/* Enhanced action buttons */
.profile-action-button {
    background: linear-gradient(135deg, #1e40af, #1d4ed8);
    border: 1px solid #1e3a8a;
    border-radius: 6px;
    color: white;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 
        0 2px 4px rgba(0, 0, 0, 0.1),
        0 1px 2px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.profile-action-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.profile-action-button:hover {
    background: linear-gradient(135deg, #1d4ed8, #2563eb);
    border-color: #1e40af;
    transform: translateY(-1px);
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.15),
        0 2px 4px rgba(0, 0, 0, 0.1);
    color: white;
    text-decoration: none;
}

.profile-action-button:hover::before {
    left: 100%;
}

.profile-action-button.secondary {
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border-color: #d1d5db;
    color: #374151;
}

.profile-action-button.secondary:hover {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border-color: #9ca3af;
    color: #1f2937;
}

.profile-action-button.danger {
    background: linear-gradient(135deg, #dc2626, #ef4444);
    border-color: #b91c1c;
}

.profile-action-button.danger:hover {
    background: linear-gradient(135deg, #b91c1c, #dc2626);
    border-color: #991b1b;
}

/* Professional Info Display - Compact Spacing */
.profile-info-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    color: #6b7280;
    transition: all 0.2s ease;
}

.profile-info-item:last-child {
    border-bottom: none;
}

.profile-info-icon {
    width: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.625rem;
    color: #1e40af;
    font-size: 0.8125rem;
}

.profile-info-text {
    color: #374151;
    font-size: 0.8125rem;
    font-weight: 500;
}

/* Enhanced file upload styling */
.profile-file-upload {
    position: relative;
    display: inline-block;
    cursor: pointer;
    width: 100%;
}

.profile-file-upload input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.profile-file-upload-label {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    background: #fafbff;
    transition: all 0.3s ease;
    cursor: pointer;
}

.profile-file-upload:hover .profile-file-upload-label {
    border-color: #1e40af;
    background: #f0f4ff;
}

/* Compact professional section headers */
.profile-section-header {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border-bottom: 2px solid #e2e8f0;
    padding: 0.75rem 1.25rem;
    border-radius: 12px 12px 0 0;
}

.profile-section-title {
    color: #1f2937;
    font-weight: 600;
    font-size: 1rem;
    margin: 0;
    display: flex;
    align-items: center;
}

.profile-section-title i {
    color: #1e40af;
    margin-right: 0.5rem;
    font-size: 0.875rem;
}

.profile-section-subtitle {
    color: #6b7280;
    font-size: 0.8125rem;
    margin: 0.125rem 0 0 0;
}

/* Government-Level Responsive Design */
@media (max-width: 1024px) {
    .profile-avatar,
    .profile-avatar-placeholder {
        width: 56px;
        height: 56px;
    }

    .profile-avatar-placeholder i {
        font-size: 1.25rem;
    }

    .profile-avatar-proper,
    .profile-avatar-placeholder-proper {
        width: 88px;
        height: 88px;
    }

    .profile-avatar-placeholder-proper i {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .profile-card {
        border-radius: 8px;
        margin-bottom: 1rem;
    }

    .profile-avatar,
    .profile-avatar-placeholder {
        width: 48px;
        height: 48px;
    }

    .profile-avatar-placeholder i {
        font-size: 1rem;
    }

    .profile-action-button {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .profile-avatar-proper,
    .profile-avatar-placeholder-proper {
        width: 80px;
        height: 80px;
    }

    .profile-avatar-placeholder-proper i {
        font-size: 1.75rem;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
