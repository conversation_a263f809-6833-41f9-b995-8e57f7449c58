/* Patient Portal Specific Styles - Professional Government Theme */

/* Professional Navigation Styles - Matching Provider Dashboard */
.main-navigation {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.3);
    box-shadow:
        0 4px 20px rgba(14, 165, 233, 0.08),
        0 1px 3px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 50;
}

.main-navigation::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #0ea5e9, #22c55e, #0ea5e9);
    background-size: 200% 100%;
    animation: shimmerNav 6s ease-in-out infinite;
}

@keyframes shimmerNav {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.nav-logo {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, #0284c7, #0369a1);
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 4px 12px rgba(2, 132, 199, 0.3);
    transition: all 0.3s ease;
}

.nav-logo:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(2, 132, 199, 0.4);
}

.nav-link {
    position: relative;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    font-weight: 500;
    text-decoration: none;
}

.nav-link:hover {
    background: rgba(2, 132, 199, 0.1);
    transform: translateY(-1px);
}

.nav-link.active {
    background: rgba(2, 132, 199, 0.1);
    color: #0284c7 !important;
}

/* Mobile Navigation */
#mobile-menu {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(229, 231, 235, 0.8);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

#mobile-menu-button {
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

#mobile-menu-button:hover {
    background: rgba(2, 132, 199, 0.1);
}

/* Professional Authentication Forms */
.auth-input {
    width: 100% !important;
    padding-left: 2.5rem !important;
    padding-right: 1rem !important;
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important;
    border: 1px solid #d1d5db !important;
    border-radius: 0.5rem !important;
    font-size: 0.875rem !important;
    line-height: 1.25rem !important;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95)) !important;
    backdrop-filter: blur(10px) !important;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06) !important;
}

.auth-input:focus {
    outline: none !important;
    border-color: #0284c7 !important;
    box-shadow: 0 0 0 3px rgba(2, 132, 199, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    background: rgba(255, 255, 255, 1) !important;
}

.auth-input::placeholder {
    color: #9ca3af;
    font-size: 0.875rem;
}

.auth-checkbox {
    height: 1rem;
    width: 1rem;
    color: #0284c7;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    transition: all 0.2s ease;
}

.auth-checkbox:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(2, 132, 199, 0.1);
}

.auth-checkbox:checked {
    background-color: #0284c7;
    border-color: #0284c7;
}

/* Professional Authentication Form Container */
.auth-form-container {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95)) !important;
    backdrop-filter: blur(25px) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    box-shadow:
        0 20px 40px -12px rgba(14, 165, 233, 0.15),
        0 8px 25px -8px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    border-radius: 16px !important;
    padding: 2rem !important;
    position: relative !important;
    z-index: 10 !important;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    overflow: hidden !important;
}

.auth-form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.02) 0%, rgba(34, 197, 94, 0.02) 100%);
    pointer-events: none;
    z-index: -1;
}

.auth-form-container:hover {
    transform: translateY(-2px);
    box-shadow:
        0 25px 50px -12px rgba(14, 165, 233, 0.2),
        0 12px 30px -8px rgba(0, 0, 0, 0.12),
        0 0 0 1px rgba(255, 255, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

/* Patient Portal Professional Card Styling */
.patient-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 0 0 16px 16px;
    box-shadow:
        0 8px 25px rgba(14, 165, 233, 0.08),
        0 4px 12px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    padding: 1.5rem;
}

.patient-card:hover {
    transform: translateY(-2px);
    box-shadow:
        0 12px 35px rgba(14, 165, 233, 0.12),
        0 6px 18px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

.patient-stat-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 0 0 16px 16px;
    box-shadow:
        0 8px 25px rgba(14, 165, 233, 0.08),
        0 4px 12px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    min-height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 1.5rem;
}

.patient-header {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.2);
    position: relative;
    z-index: 10;
    padding: 2rem;
    margin-bottom: 2rem;
    border-radius: 0 0 16px 16px;
}

.read-only-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    color: #166534;
    border: 1px solid #86efac;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Professional QR Scanner Styles */
.qr-scanner-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.6) !important;
    backdrop-filter: blur(8px) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 9999 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
}

.qr-scanner-overlay.active {
    opacity: 1 !important;
    visibility: visible !important;
}

.qr-scanner-modal {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    box-shadow:
        0 20px 40px rgba(14, 165, 233, 0.15),
        0 8px 25px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
    position: relative;
}

.qr-scanner-overlay.active .qr-scanner-modal {
    transform: scale(1);
}

.qr-modal-header {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.2);
    color: #1f2937;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.qr-header-content {
    flex: 1;
}

.qr-success-indicator {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.qr-icon-container {
    width: 3rem;
    height: 3rem;
    background: rgba(14, 165, 233, 0.1);
    border: 1px solid rgba(14, 165, 233, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: #0284c7;
}

.qr-modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    color: #1f2937;
}

.qr-modal-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0.25rem 0 0 0;
}

.qr-close-btn {
    background: rgba(107, 114, 128, 0.1);
    border: 1px solid rgba(107, 114, 128, 0.2);
    color: #6b7280;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.qr-close-btn:hover {
    background: rgba(107, 114, 128, 0.2);
    color: #374151;
}

.qr-modal-body {
    padding: 1.5rem;
}

.qr-video-container {
    position: relative;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 1rem;
}

#qr-video {
    width: 100%;
    height: 300px;
    object-fit: cover;
}

.qr-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.qr-scanner-frame {
    width: 200px;
    height: 200px;
    border: 3px solid #0284c7;
    border-radius: 12px;
    position: relative;
    animation: pulse 2s infinite;
}

.qr-scanner-frame::before,
.qr-scanner-frame::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border: 3px solid #0284c7;
}

.qr-scanner-frame::before {
    top: -3px;
    left: -3px;
    border-right: none;
    border-bottom: none;
}

.qr-scanner-frame::after {
    bottom: -3px;
    right: -3px;
    border-left: none;
    border-top: none;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.qr-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.qr-status.success {
    background: #dcfce7;
    color: #166534;
    border-color: #bbf7d0;
}

.qr-status.error {
    background: #fef2f2;
    color: #dc2626;
    border-color: #fecaca;
}

.qr-status.warning {
    background: #fffbeb;
    color: #d97706;
    border-color: #fed7aa;
}

.qr-status.info {
    background: #f0f9ff;
    color: #0284c7;
    border-color: #bae6fd;
}

/* Professional Government Style Components for Patient Portal */
.government-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    transition: all 0.2s ease;
}

.government-input:focus {
    outline: none;
    border-color: #0284c7;
    box-shadow: 0 0 0 3px rgba(2, 132, 199, 0.1);
}

.government-select-compact {
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    background: white;
    color: #111827;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.government-select-compact:focus {
    outline: none;
    border-color: #0284c7;
    box-shadow: 0 0 0 3px rgba(2, 132, 199, 0.1);
}

.government-search-input-compact {
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    transition: all 0.2s ease;
}

.government-search-input-compact:focus {
    outline: none;
    border-color: #0284c7;
    box-shadow: 0 0 0 3px rgba(2, 132, 199, 0.1);
}

.government-filter-button {
    display: inline-flex !important;
    align-items: center !important;
    padding: 0.5rem 1rem !important;
    border: 1px solid transparent !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    border-radius: 0.5rem !important;
    color: white !important;
    background: linear-gradient(135deg, #0284c7, #0369a1) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.2s ease !important;
    cursor: pointer !important;
    text-decoration: none !important;
}

.government-filter-button:hover {
    background: linear-gradient(135deg, #0369a1, #075985);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.government-filter-button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(2, 132, 199, 0.3);
}

/* Professional Button Variants */
.btn-primary {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #0284c7, #0369a1);
    color: white;
    font-weight: 500;
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    text-decoration: none;
    cursor: pointer;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0369a1, #075985);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    color: white;
    text-decoration: none;
}

.btn-secondary {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    color: #475569;
    font-weight: 500;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    text-decoration: none;
    cursor: pointer;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    color: #334155;
    text-decoration: none;
}

/* Button Sizes */
.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
}

/* Button Groups */
.btn-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* Professional Page Layout */
.page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
    min-height: calc(100vh - 120px);
}

.page-header {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(229, 231, 235, 0.8);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    color: #6b7280;
    font-size: 1.125rem;
    margin-bottom: 0;
}

.content-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(229, 231, 235, 0.8);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.content-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* Professional Form Styling */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.form-input:focus {
    outline: none;
    border-color: #0284c7;
    box-shadow: 0 0 0 3px rgba(2, 132, 199, 0.1);
    background: rgba(255, 255, 255, 1);
}

/* Professional Tables */
.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.data-table th {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid #f3f4f6;
    color: #6b7280;
}

.data-table tr:hover {
    background: rgba(2, 132, 199, 0.05);
}

/* General Modal Styles for Patient Portal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active,
.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    box-shadow:
        0 20px 40px rgba(14, 165, 233, 0.15),
        0 8px 25px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
    position: relative;
}

.modal-overlay.active .modal-container,
.modal-overlay.show .modal-container {
    transform: scale(1);
}

.modal-header {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.2);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.modal-close {
    background: rgba(107, 114, 128, 0.1);
    border: 1px solid rgba(107, 114, 128, 0.2);
    color: #6b7280;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: rgba(107, 114, 128, 0.2);
    color: #374151;
}

.modal-body {
    padding: 1.5rem;
}

/* Patient Portal Responsive Design */
@media (max-width: 768px) {
    .patient-header {
        padding: 1rem;
    }
    
    .patient-header h1 {
        font-size: 1.5rem;
    }
    
    .patient-card {
        margin: 0.5rem;
        padding: 1rem;
    }
    
    .qr-scanner-modal {
        width: 95%;
        margin: 1rem;
    }
    
    #qr-video {
        height: 250px;
    }
    
    .qr-scanner-frame {
        width: 150px;
        height: 150px;
    }
}

/* Print Styles */
@media print {
    .patient-nav,
    .qr-scanner-overlay,
    .government-filter-button,
    .read-only-badge {
        display: none !important;
    }
    
    .patient-card {
        box-shadow: none;
        border: 1px solid #ccc;
        margin-bottom: 1rem;
    }
    
    .patient-header {
        background: none !important;
        color: black !important;
        border-bottom: 2px solid #ccc;
    }
}
