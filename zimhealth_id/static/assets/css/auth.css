/* Professional Authentication Styles for ZimHealth-ID */

/* Government-grade authentication layout */
.auth-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
    overflow: hidden;
}

/* Sophisticated background pattern */
.auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(14, 165, 233, 0.02) 0%, transparent 50%);
    background-size: 800px 800px, 600px 600px, 400px 400px;
    animation: backgroundFloat 20s ease-in-out infinite alternate;
}

@keyframes backgroundFloat {
    0% { transform: translate(0, 0) rotate(0deg); }
    100% { transform: translate(-10px, -10px) rotate(1deg); }
}

/* Geometric background elements */
.auth-container::after {
    content: '';
    position: absolute;
    top: 10%;
    right: 10%;
    width: 200px;
    height: 200px;
    background: linear-gradient(45deg, rgba(14, 165, 233, 0.05), rgba(34, 197, 94, 0.05));
    border-radius: 50%;
    animation: geometricFloat 15s ease-in-out infinite alternate-reverse;
}

@keyframes geometricFloat {
    0% { transform: translate(0, 0) scale(1); }
    100% { transform: translate(20px, -20px) scale(1.1); }
}

/* Modern compact form container */
.auth-form-container {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow:
        0 20px 40px -12px rgba(14, 165, 233, 0.15),
        0 8px 25px -8px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    max-width: 380px;
    width: 100%;
    position: relative;
    z-index: 10;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    padding: 0.75rem;
    overflow: hidden;
}

.auth-form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #0ea5e9, #22c55e, #0ea5e9);
    background-size: 200% 100%;
    animation: shimmerTop 3s ease-in-out infinite;
}

.auth-form-container:hover {
    transform: translateY(-2px);
    box-shadow:
        0 25px 50px -12px rgba(14, 165, 233, 0.2),
        0 12px 30px -8px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

@keyframes shimmerTop {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Modern enhanced header styling */
.auth-header {
    text-align: center;
    margin-bottom: 0.75rem;
    opacity: 0;
    animation: fadeInUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s forwards;
    position: relative;
}

.auth-logo {
    width: 52px;
    height: 52px;
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #22c55e 100%);
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.75rem;
    position: relative;
    box-shadow:
        0 8px 25px rgba(14, 165, 233, 0.4),
        0 4px 12px rgba(34, 197, 94, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    animation: logoFloat 4s ease-in-out infinite;
}

.auth-logo::before {
    content: '';
    position: absolute;
    inset: 3px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border-radius: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.auth-logo::after {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.3), rgba(34, 197, 94, 0.3));
    border-radius: 16px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.auth-logo:hover::after {
    opacity: 1;
}

.auth-logo i {
    color: #0ea5e9;
    font-size: 1.25rem;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

.auth-logo:hover i {
    transform: scale(1.1);
    color: #0284c7;
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-2px); }
}

/* Back navigation styling */
.back-nav {
    position: absolute;
    top: 1rem;
    left: 1rem;
    z-index: 20;
}

.back-nav-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.back-nav-link:hover {
    color: #0ea5e9;
    background: rgba(255, 255, 255, 0.95);
    transform: translateX(-2px);
}

/* Form animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Modern organized input styling */
.auth-input-group {
    margin-bottom: 0.75rem;
    opacity: 0;
    animation: slideInLeft 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.auth-input-group:nth-child(1) { animation-delay: 0.3s; }
.auth-input-group:nth-child(2) { animation-delay: 0.4s; }
.auth-input-group:nth-child(3) { animation-delay: 0.5s; }
.auth-input-group:nth-child(4) { animation-delay: 0.6s; }

.auth-input-group.compact {
    margin-bottom: 0.5rem;
}

.auth-input-group.ultra-compact {
    margin-bottom: 0.375rem;
}

/* Specific spacing adjustments for checkbox */
.auth-input-group.checkbox-compact {
    margin-bottom: 0.25rem;
    margin-top: 0.125rem;
}

/* Content organization sections */
.auth-section {
    position: relative;
    margin-bottom: 1rem;
}

.auth-section.credentials {
    padding-top: 0.375rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid rgba(229, 231, 235, 0.3);
    margin-bottom: 0.5rem;
}

.auth-section.options {
    padding-top: 0.125rem;
    padding-bottom: 0.125rem;
    margin-bottom: 0.375rem;
}

.auth-section.actions {
    padding-top: 0.25rem;
}

/* Section divider */
.section-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(14, 165, 233, 0.2), transparent);
    margin: 0.75rem 0;
    position: relative;
}

.section-divider::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 4px;
    height: 4px;
    background: #0ea5e9;
    border-radius: 50%;
}

.auth-label {
    display: block;
    font-size: 0.8rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
    transition: color 0.3s ease;
}

.auth-input-wrapper {
    position: relative;
}

.auth-input {
    width: 100%;
    padding: 0.625rem 0.75rem;
    padding-left: 2.25rem;
    border: 2px solid rgba(229, 231, 235, 0.6);
    border-radius: 10px;
    font-size: 0.8rem;
    background: linear-gradient(145deg, #ffffff, #f8fafc);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    outline: none;
    position: relative;
}

.auth-input::placeholder {
    color: #9ca3af;
    transition: color 0.3s ease;
}

.auth-input:focus {
    border-color: #0ea5e9;
    background: #ffffff;
    box-shadow:
        0 0 0 3px rgba(14, 165, 233, 0.15),
        0 6px 20px rgba(14, 165, 233, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

.auth-input:focus + .auth-input-icon {
    color: #0ea5e9;
    transform: scale(1.1);
}

.auth-input:hover {
    border-color: rgba(14, 165, 233, 0.3);
    background: #ffffff;
}

.auth-input-icon {
    position: absolute;
    left: 0.625rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    pointer-events: none;
    font-size: 0.875rem;
}

.auth-input-action {
    position: absolute;
    right: 0.625rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    font-size: 0.875rem;
}

.auth-input-action:hover {
    color: #0ea5e9;
    background: rgba(14, 165, 233, 0.1);
    transform: translateY(-50%) scale(1.1);
}

/* Modern enhanced button styling */
.auth-button {
    width: 100%;
    padding: 0.625rem 1rem;
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    box-shadow:
        0 4px 15px rgba(14, 165, 233, 0.4),
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.auth-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.auth-button:hover {
    transform: translateY(-2px);
    box-shadow:
        0 8px 25px rgba(14, 165, 233, 0.5),
        0 4px 12px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    background: linear-gradient(135deg, #0284c7 0%, #0369a1 50%, #075985 100%);
}

.auth-button:hover::before {
    left: 100%;
}

.auth-button:active {
    transform: translateY(-1px);
}

/* Loading state */
.auth-button.loading {
    pointer-events: none;
    opacity: 0.8;
}

.auth-button.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Error states */
.auth-error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 0.75rem;
    border-radius: 8px;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-4px); }
    75% { transform: translateX(4px); }
}

/* Professional links */
.auth-link {
    color: #0ea5e9;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    position: relative;
}

.auth-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: #0ea5e9;
    transition: width 0.3s ease;
}

.auth-link:hover {
    color: #0284c7;
}

.auth-link:hover::after {
    width: 100%;
}

/* Ultra-compact security indicators */
.security-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 500;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

/* Ultra-compact security footer */
.security-footer {
    padding-top: 0.5rem;
    border-top: 1px solid rgba(229, 231, 235, 0.5);
    margin-top: 0.5rem;
}

.security-badges {
    display: flex;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 0.375rem;
}

.security-badge-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.7rem;
    color: #6b7280;
}

.security-badge-item i {
    color: #22c55e;
    font-size: 0.7rem;
}

/* Ultra-compact additional links section */
.auth-links {
    padding-top: 0.5rem;
    border-top: 1px solid rgba(229, 231, 235, 0.3);
    margin-top: 0.5rem;
}

.auth-links-divider {
    position: relative;
    margin: 0.5rem 0;
}

.auth-links-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: rgba(229, 231, 235, 0.5);
}

.auth-links-divider span {
    position: relative;
    background: white;
    padding: 0 0.75rem;
    font-size: 0.7rem;
    color: #9ca3af;
}

/* Ultra-compact responsive design */
@media (max-width: 640px) {
    .auth-form-container {
        margin: 0.75rem;
        max-width: none;
        padding: 0.625rem;
    }

    .auth-container::after {
        width: 80px;
        height: 80px;
        top: 5%;
        right: 5%;
    }

    .back-nav {
        top: 0.375rem;
        left: 0.375rem;
    }

    .back-nav-link {
        padding: 0.25rem 0.375rem;
        font-size: 0.75rem;
    }

    .security-badges {
        flex-wrap: wrap;
        gap: 0.375rem;
        justify-content: center;
    }

    .auth-input {
        padding: 0.5rem 0.625rem;
        padding-left: 2rem;
        font-size: 0.75rem;
    }

    .auth-button {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
    }
}

@media (max-height: 700px) {
    .auth-container {
        padding: 0.5rem 0;
    }

    .auth-header {
        margin-bottom: 0.5rem;
    }

    .auth-input-group {
        margin-bottom: 0.5rem;
    }

    .auth-input-group.compact {
        margin-bottom: 0.375rem;
    }

    .auth-input-group.ultra-compact {
        margin-bottom: 0.25rem;
    }
}

@media (max-height: 600px) {
    .auth-logo {
        width: 28px;
        height: 28px;
        margin-bottom: 0.25rem;
    }

    .auth-logo i {
        font-size: 0.875rem;
    }

    .security-footer {
        padding-top: 0.25rem;
        margin-top: 0.25rem;
    }

    .security-badges {
        margin-bottom: 0.25rem;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .auth-header,
    .auth-input-group {
        opacity: 1;
        animation: none;
    }
}
