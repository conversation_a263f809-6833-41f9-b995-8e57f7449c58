/* Professional Security Page Styles for ZimHealth-ID */

/* Government-grade security layout */
.security-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    position: relative;
}

/* Sophisticated background pattern */
.security-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.02) 0%, transparent 50%);
    background-size: 1200px 1200px, 800px 800px;
    pointer-events: none;
    z-index: 0;
}

/* Professional security header */
.security-header {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.2);
    position: relative;
    z-index: 10;
}

.security-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #0ea5e9, #22c55e, #0ea5e9);
    background-size: 200% 100%;
    animation: shimmerSecurity 6s ease-in-out infinite;
}

@keyframes shimmerSecurity {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Security status indicator */
.security-status-indicator {
    display: flex;
    align-items: center;
    space-x: 0.5rem;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05));
    border: 1px solid rgba(34, 197, 94, 0.2);
    border-radius: 8px;
    padding: 0.5rem 1rem;
}

/* Security cards */
.security-card,
.security-nav-card,
.security-tips-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border: 1px solid rgba(229, 231, 235, 0.3);
    border-radius: 16px;
    box-shadow:
        0 20px 40px -12px rgba(14, 165, 233, 0.15),
        0 8px 25px -8px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.security-card:hover,
.security-nav-card:hover,
.security-tips-card:hover {
    transform: translateY(-2px);
    box-shadow:
        0 25px 50px -12px rgba(14, 165, 233, 0.25),
        0 12px 30px -8px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Security section headers */
.security-section-header {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
    border-bottom: 1px solid rgba(229, 231, 235, 0.3);
    padding: 1.5rem;
    border-radius: 16px 16px 0 0;
}

.security-section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    display: flex;
    align-items: center;
    margin: 0;
}

.security-section-title i {
    margin-right: 0.75rem;
    color: #0ea5e9;
    font-size: 1.25rem;
}

.security-section-subtitle {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0.5rem 0 0 0;
    line-height: 1.4;
}

/* Security navigation items */
.security-nav-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
    text-decoration: none;
    color: inherit;
    border: 1px solid transparent;
}

.security-nav-item:hover {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.08), rgba(14, 165, 233, 0.03));
    border-color: rgba(14, 165, 233, 0.2);
    transform: translateX(4px);
}

.security-nav-item.active {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.15), rgba(14, 165, 233, 0.05));
    border-color: rgba(14, 165, 233, 0.3);
    box-shadow: 0 4px 12px rgba(14, 165, 233, 0.2);
}

.security-nav-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.security-nav-item.disabled:hover {
    background: none;
    border-color: transparent;
    transform: none;
}

.security-nav-item i {
    color: #0ea5e9;
    font-size: 1.25rem;
    width: 1.5rem;
    text-align: center;
}

.security-nav-title {
    font-weight: 600;
    color: #111827;
    font-size: 0.875rem;
    display: block;
}

.security-nav-desc {
    color: #6b7280;
    font-size: 0.75rem;
    display: block;
    margin-top: 0.25rem;
}

/* Security form styling */
.security-form-group {
    margin-bottom: 1.5rem;
}

.security-form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.security-form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 12px;
    font-size: 0.875rem;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
}

.security-form-input:focus {
    outline: none;
    border-color: #0ea5e9;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
    background: rgba(255, 255, 255, 1);
}

.password-toggle-btn {
    color: #6b7280;
    transition: color 0.3s ease;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.25rem;
}

.password-toggle-btn:hover {
    color: #374151;
}

/* Security form errors */
.security-form-error {
    margin-top: 0.5rem;
    color: #dc2626;
    font-size: 0.875rem;
}

.security-form-error-global {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

/* Security requirements card */
.security-requirements-card {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.08), rgba(14, 165, 233, 0.03));
    border: 1px solid rgba(14, 165, 233, 0.2);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

/* Security form actions */
.security-form-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(229, 231, 235, 0.3);
}

@media (min-width: 640px) {
    .security-form-actions {
        flex-direction: row;
    }
}

.security-action-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    text-decoration: none;
    border: none;
    cursor: pointer;
}

.security-action-button.primary {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(14, 165, 233, 0.4);
}

.security-action-button.primary:hover {
    background: linear-gradient(135deg, #0284c7 0%, #0369a1 50%, #075985 100%);
    box-shadow: 0 6px 20px rgba(14, 165, 233, 0.5);
    transform: translateY(-2px);
}

.security-action-button.secondary {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 50%, #374151 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(107, 114, 128, 0.4);
}

.security-action-button.secondary:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 50%, #1f2937 100%);
    box-shadow: 0 6px 20px rgba(107, 114, 128, 0.5);
    transform: translateY(-2px);
}

/* Security tips styling */
.security-tip-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.6));
    border: 1px solid rgba(229, 231, 235, 0.3);
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.security-tip-item:hover {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.05), rgba(14, 165, 233, 0.02));
    border-color: rgba(14, 165, 233, 0.2);
    transform: translateY(-2px);
}

.security-tip-item i {
    font-size: 1.25rem;
    margin-top: 0.125rem;
}

.security-tip-title {
    font-weight: 600;
    color: #111827;
    font-size: 0.875rem;
    margin: 0 0 0.25rem 0;
}

.security-tip-desc {
    color: #6b7280;
    font-size: 0.75rem;
    margin: 0;
    line-height: 1.4;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .security-container {
        padding: 0;
    }
    
    .security-header {
        border-radius: 0;
    }
    
    .security-card,
    .security-nav-card,
    .security-tips-card {
        border-radius: 12px;
        margin: 0 0.5rem;
    }
    
    .security-section-header {
        padding: 1rem;
    }
    
    .security-nav-item {
        padding: 0.75rem;
    }
    
    .security-tip-item {
        padding: 0.75rem;
    }
}
