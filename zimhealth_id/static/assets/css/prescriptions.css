/* Professional Prescriptions Page Styles for ZimHealth-ID */

/* Government-grade prescriptions layout */
.prescriptions-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    position: relative;
}

/* Sophisticated background pattern */
.prescriptions-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.02) 0%, transparent 50%);
    background-size: 1200px 1200px, 800px 800px;
    pointer-events: none;
    z-index: 0;
}

/* Enhanced prescriptions header styling */
.prescriptions-header {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.2);
    position: relative;
    z-index: 10;
}

/* Professional stats cards */
.stats-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    box-shadow: 
        0 8px 25px rgba(14, 165, 233, 0.06),
        0 4px 12px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 12px 30px rgba(14, 165, 233, 0.08),
        0 6px 16px rgba(0, 0, 0, 0.06);
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.stats-icon.active {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(139, 92, 246, 0.05));
    color: #8b5cf6;
}

.stats-icon.expiring {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.15), rgba(245, 158, 11, 0.05));
    color: #f59e0b;
}

.stats-icon.completed {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(34, 197, 94, 0.05));
    color: #22c55e;
}

.stats-icon.discontinued {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(239, 68, 68, 0.05));
    color: #ef4444;
}

/* Government-Level Professional Search Controls */
.prescriptions-search-input {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.875rem;
    color: #374151;
    transition: border-color 0.2s ease;
}

.prescriptions-search-input:focus {
    outline: none;
    border-color: #6b7280;
    box-shadow: 0 0 0 1px #6b7280;
}

.prescriptions-filter-button {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
}

.prescriptions-filter-button:hover {
    border-color: #9ca3af;
    background: #f9fafb;
}

.prescriptions-filter-button:focus {
    outline: none;
    border-color: #6b7280;
    box-shadow: 0 0 0 1px #6b7280;
}

/* QR Scanner Button inside Prescriptions Search Bar */
.prescriptions-search-qr-button {
    color: #6b7280;
    cursor: pointer;
    transition: color 0.2s ease;
    padding: 0.5rem;
    border-radius: 3px;
}

.prescriptions-search-qr-button:hover {
    color: #374151;
    background: rgba(243, 244, 246, 0.5);
}

.prescriptions-search-qr-button:focus {
    outline: none;
    color: #1f2937;
    background: rgba(243, 244, 246, 0.8);
}

/* Professional prescriptions table */
.prescriptions-table-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.prescriptions-table-header {
    background: #f9fafb;
    border-bottom: 2px solid #e5e7eb;
    padding: 1rem 1.5rem;
}

.prescriptions-table-header h3 {
    color: #111827;
    font-weight: 600;
    font-size: 1rem;
    margin: 0;
}



.prescriptions-table thead {
    background: #f3f4f6;
    border-bottom: 2px solid #e5e7eb;
}

.prescriptions-table th {
    padding: 1rem 1.25rem;
    text-align: left;
    font-weight: 600;
    color: #374151;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    border-right: 1px solid #f3f4f6;
    white-space: nowrap;
}

.prescriptions-table th:last-child {
    border-right: none;
}

.prescriptions-table tbody tr {
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    transition: background-color 0.2s ease;
}

.prescriptions-table tbody tr:nth-child(even) {
    background: #fafafa;
}

.prescriptions-table tbody tr:hover {
    background: #f3f4f6;
}

.prescriptions-table td {
    padding: 1rem 1.25rem;
    border-right: 1px solid #f9fafb;
    color: #374151;
    font-size: 0.875rem;
    vertical-align: middle;
}

.prescriptions-table td:last-child {
    border-right: none;
}

/* Simple column alignment - no complex widths */
.prescriptions-table th,
.prescriptions-table td {
    padding: 1rem 1.25rem;
    border-right: 1px solid #f9fafb;
    color: #374151;
    font-size: 0.875rem;
    vertical-align: middle;
}

.prescriptions-table th:last-child,
.prescriptions-table td:last-child {
    border-right: none;
}

/* Column-specific alignment only */
.prescriptions-table .medication-column {
    text-align: left;
}

.prescriptions-table .patient-column {
    text-align: left;
}

.prescriptions-table .dosage-column,
.prescriptions-table .frequency-column,
.prescriptions-table .status-column,
.prescriptions-table .doctor-column,
.prescriptions-table .actions-column {
    text-align: center;
}

/* Government-Level Professional Status Indicators */
.prescription-status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.prescription-status-indicator.active {
    background: #8b5cf6;
}

.prescription-status-indicator.completed {
    background: #22c55e;
}

.prescription-status-indicator.discontinued {
    background: #ef4444;
}

.prescription-status-indicator.on_hold {
    background: #f59e0b;
}

/* Professional medication avatar */
.prescription-avatar {
    width: 40px;
    height: 40px;
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.prescription-avatar i {
    color: #6b7280;
    font-size: 1rem;
}

/* Text container overflow handling */
.prescriptions-table .medication-column .min-w-0,
.prescriptions-table .patient-column .min-w-0 {
    max-width: calc(100% - 60px);
}

.prescriptions-table .medication-column .text-sm,
.prescriptions-table .patient-column .text-sm {
    white-space: normal;
    word-wrap: break-word;
    line-height: 1.3;
}

.prescriptions-table .medication-column .text-xs,
.prescriptions-table .patient-column .text-xs {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Government-Level Professional Action Buttons */
.prescription-action-button {
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: 1px solid #d1d5db;
    background: #ffffff;
    color: #6b7280;
    font-size: 0.875rem;
}

.prescription-action-button:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
}

.prescription-action-button.view {
    color: #8b5cf6;
}

.prescription-action-button.view:hover {
    background: rgba(139, 92, 246, 0.05);
    border-color: rgba(139, 92, 246, 0.2);
}

.prescription-action-button.complete {
    color: #22c55e;
}

.prescription-action-button.complete:hover {
    background: rgba(34, 197, 94, 0.05);
    border-color: rgba(34, 197, 94, 0.2);
}

.prescription-action-button.hold {
    color: #f59e0b;
}

.prescription-action-button.hold:hover {
    background: rgba(245, 158, 11, 0.05);
    border-color: rgba(245, 158, 11, 0.2);
}

.prescription-action-button.discontinue {
    color: #ef4444;
}

.prescription-action-button.discontinue:hover {
    background: rgba(239, 68, 68, 0.05);
    border-color: rgba(239, 68, 68, 0.2);
}

.prescription-action-button.print {
    color: #0ea5e9;
}

.prescription-action-button.print:hover {
    background: rgba(14, 165, 233, 0.05);
    border-color: rgba(14, 165, 233, 0.2);
}

.prescription-action-button.edit {
    color: #6b7280;
}

.prescription-action-button.edit:hover {
    background: rgba(107, 114, 128, 0.05);
    border-color: rgba(107, 114, 128, 0.2);
}

/* Professional status badges */
.prescription-status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.75rem;
    display: inline-block;
    border: 1px solid;
}

.prescription-status-badge.active {
    background: rgba(139, 92, 246, 0.1);
    border-color: rgba(139, 92, 246, 0.2);
    color: #8b5cf6;
}

.prescription-status-badge.completed {
    background: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.prescription-status-badge.discontinued {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.prescription-status-badge.on_hold {
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

/* Professional frequency badges */
.frequency-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 500;
    font-size: 0.75rem;
    display: inline-block;
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    color: #374151;
}

/* Enhanced New Prescription Button */
.new-prescription-button {
    background: linear-gradient(135deg, #1e40af, #1d4ed8);
    border: 1px solid #1e3a8a;
    border-radius: 6px;
    color: white;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 
        0 2px 4px rgba(0, 0, 0, 0.1),
        0 1px 2px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
}

.new-prescription-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.new-prescription-button:hover {
    background: linear-gradient(135deg, #1d4ed8, #2563eb);
    border-color: #1e40af;
    transform: translateY(-1px);
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.15),
        0 2px 4px rgba(0, 0, 0, 0.1);
}

.new-prescription-button:hover::before {
    left: 100%;
}

/* Details row styling */
.prescriptions-table .details-row {
    background: #f9fafb !important;
}

.prescriptions-table .details-row:hover {
    background: #f3f4f6 !important;
}

.prescriptions-table .details-row td {
    border-top: none;
    font-size: 0.75rem;
    color: #6b7280;
    padding: 0.75rem 1.25rem;
}

/* Enhanced table borders and spacing */
.prescriptions-table {
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.prescriptions-table thead th:first-child {
    border-top-left-radius: 4px;
}

.prescriptions-table thead th:last-child {
    border-top-right-radius: 4px;
}

/* Professional table hover effects */
.prescriptions-table tbody tr:not(.details-row):hover {
    background: #f8fafc !important;
    box-shadow: inset 0 0 0 1px rgba(139, 92, 246, 0.1);
}

/* Enhanced status indicator positioning */
.prescription-status-indicator {
    flex-shrink: 0;
}

/* Clean table structure */
.prescriptions-table {
    width: 100%;
    border-collapse: collapse;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

/* Enhanced pagination */
.pagination-button {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    transition: all 0.2s ease;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    color: #374151;
}

.pagination-button:hover {
    background: #f9fafb;
    border-color: #9ca3af;
}

/* Priority indicators */
.priority-indicator {
    font-size: 0.75rem;
    font-weight: 600;
}

.priority-indicator.high {
    color: #dc2626;
}

.priority-indicator.urgent {
    color: #991b1b;
}

.priority-indicator.normal {
    color: #6b7280;
}

/* Government-Level Responsive Design */
@media (max-width: 1024px) {
    .prescriptions-table .patient-column,
    .prescriptions-table .doctor-column {
        min-width: 120px;
    }

    .prescriptions-table .actions-column {
        min-width: 140px;
    }

    .prescriptions-table .medication-column {
        max-width: 200px;
    }
}

@media (max-width: 768px) {
    .prescriptions-table {
        font-size: 0.8rem;
    }

    .prescriptions-table th,
    .prescriptions-table td {
        padding: 0.75rem 0.5rem;
    }

    .prescriptions-table .medication-column {
        min-width: 160px;
    }

    .prescriptions-table .patient-column {
        min-width: 120px;
    }

    .prescriptions-table .actions-column {
        min-width: 120px;
    }

    .prescription-action-button {
        width: 32px;
        height: 32px;
        font-size: 0.75rem;
    }

    .prescriptions-filter-button {
        min-width: 100px;
        font-size: 0.75rem;
    }

    .prescriptions-search-input {
        font-size: 0.8rem;
    }
}

@media (max-width: 640px) {
    .stats-card {
        border-radius: 8px;
    }

    .stats-icon {
        width: 40px;
        height: 40px;
    }
}

/* QR Code Scanner Modal Styles */
.qr-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.qr-modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.qr-modal-content {
    background: white;
    border-radius: 12px;
    padding: 24px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease;
}

#qr-video {
    width: 100%;
    height: 300px;
    border-radius: 8px;
    background: #000;
    object-fit: cover;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
