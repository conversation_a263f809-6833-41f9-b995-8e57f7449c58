/* Professional Patient QR Code Page Styles for ZimHealth-ID */

/* Single-page layout - no scrolling */
.qr-code-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    max-height: 100vh;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
}

/* Sophisticated background pattern */
.qr-code-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.02) 0%, transparent 50%);
    background-size: 1200px 1200px, 800px 800px;
    pointer-events: none;
    z-index: 0;
}

/* Use consistent patients-header styling from other pages */
.qr-code-container .patients-header {
    flex-shrink: 0;
}

/* Main content area - fills remaining space */
.qr-code-container > .max-w-7xl {
    flex: 1;
    overflow: hidden;
    max-width: none !important;
    width: 100% !important;
}

/* Grid layout optimized for single page */
.qr-content-grid {
    height: 100%;
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 1rem;
    overflow: hidden;
}

.qr-main-content {
    overflow-y: auto;
    max-height: 100%;
    padding-right: 0.5rem;
}

.qr-sidebar {
    overflow-y: auto;
    max-height: 100%;
    padding-right: 0.5rem;
}

/* QR Code display area */
.qr-display-area {
    text-align: center;
    padding: 1.5rem;
}

.qr-code-wrapper {
    display: inline-block;
    padding: 1rem;
    background: white;
    border: 2px solid rgba(229, 231, 235, 0.3);
    border-radius: 12px;
    box-shadow: 
        0 8px 25px rgba(14, 165, 233, 0.1),
        0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.qr-code-wrapper:hover {
    transform: scale(1.02);
    box-shadow: 
        0 12px 30px rgba(14, 165, 233, 0.15),
        0 6px 16px rgba(0, 0, 0, 0.08);
}

.qr-code-image {
    width: 200px;
    height: 200px;
    margin: 0 auto;
    border-radius: 8px;
}

.qr-code-id {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.75rem;
    font-weight: 600;
}

.qr-code-description {
    font-size: 0.75rem;
    color: #9ca3af;
    margin-top: 0.25rem;
}

/* Compact styling for QR Code Page - Same as Other Pages */
.qr-code-container .patients-table-card .patients-table-header {
    padding: 8px 16px !important; /* Reduced header padding */
    border-bottom: 1px solid #e5e7eb;
    flex-shrink: 0; /* Don't shrink header */
}

.qr-code-container .patients-table td {
    padding: 4px 8px !important; /* Substantially reduced cell padding */
    vertical-align: middle;
    line-height: 1.2 !important;
}

/* Compact card spacing */
.qr-code-container .patients-table-card {
    margin-bottom: 1rem !important; /* Reduced margin between cards */
}

/* Responsive design for single-page layout */
@media (max-width: 1024px) {
    .qr-content-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .qr-main-content,
    .qr-sidebar {
        max-height: none;
        overflow-y: visible;
    }
    
    .qr-code-container {
        max-height: none;
        overflow: visible;
    }
}

@media (max-width: 768px) {
    .qr-code-container > .max-w-7xl {
        padding: 0.75rem;
    }
    
    .qr-code-container .patients-table-card {
        margin-bottom: 0.5rem;
    }
    
    .qr-code-container .patients-table-card .patients-table-header {
        padding: 0.5rem 0.75rem;
    }
    
    .qr-display-area {
        padding: 1rem;
    }
    
    .qr-code-image {
        width: 160px;
        height: 160px;
    }
}

@media (max-width: 640px) {
    .qr-code-image {
        width: 140px;
        height: 140px;
    }
    
    .qr-code-container .patients-table td {
        padding: 0.25rem 0.375rem;
        font-size: 0.75rem;
    }
}

/* Print styles */
@media print {
    .qr-code-container {
        background: white;
        max-height: none;
        overflow: visible;
    }
    
    .qr-code-container::before {
        display: none;
    }
    
    .patients-header {
        background: white;
        border-bottom: 2px solid #000;
        box-shadow: none;
    }
    
    .patients-table-card {
        background: white;
        border: 1px solid #000;
        box-shadow: none;
        break-inside: avoid;
    }
    
    .patients-table-header {
        background: #f5f5f5;
        border-bottom: 1px solid #000;
    }
    
    .government-filter-button,
    .add-patient-button,
    .results-count {
        display: none !important;
    }
    
    .qr-content-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .qr-display-area {
        page-break-inside: avoid;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Custom scrollbar for overflow areas */
.qr-main-content::-webkit-scrollbar,
.qr-sidebar::-webkit-scrollbar {
    width: 4px;
}

.qr-main-content::-webkit-scrollbar-track,
.qr-sidebar::-webkit-scrollbar-track {
    background: rgba(229, 231, 235, 0.2);
    border-radius: 2px;
}

.qr-main-content::-webkit-scrollbar-thumb,
.qr-sidebar::-webkit-scrollbar-thumb {
    background: rgba(34, 197, 94, 0.3);
    border-radius: 2px;
}

.qr-main-content::-webkit-scrollbar-thumb:hover,
.qr-sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(34, 197, 94, 0.5);
}

/* Additional optimizations for single-page layout */
.qr-code-container .max-w-7xl {
    max-width: none !important;
}

/* Ensure full viewport usage on desktop */
@media (min-width: 1024px) {
    .qr-code-container .patients-table-card {
        margin-bottom: 0.5rem;
    }
    
    .qr-code-container > .max-w-7xl {
        padding: 0.5rem 1rem;
    }
    
    .qr-code-container .patients-table-card .patients-table-header {
        padding: 0.375rem 0.75rem;
    }
    
    .qr-display-area {
        padding: 1rem;
    }
}