/* Professional Medical Records Page Styles for ZimHealth-ID */

/* Government-grade medical records layout */
.medical-records-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    position: relative;
}

/* Sophisticated background pattern */
.medical-records-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.02) 0%, transparent 50%);
    background-size: 1200px 1200px, 800px 800px;
    pointer-events: none;
    z-index: 0;
}

/* Enhanced medical records header styling */
.medical-records-header {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.2);
    position: relative;
    z-index: 10;
}

/* Professional stats cards */
.stats-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    box-shadow: 
        0 8px 25px rgba(14, 165, 233, 0.06),
        0 4px 12px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 12px 30px rgba(14, 165, 233, 0.08),
        0 6px 16px rgba(0, 0, 0, 0.06);
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.stats-icon.total {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.15), rgba(14, 165, 233, 0.05));
    color: #0ea5e9;
}

.stats-icon.recent {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(34, 197, 94, 0.05));
    color: #22c55e;
}

.stats-icon.critical {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(239, 68, 68, 0.05));
    color: #ef4444;
}

.stats-icon.pending {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.15), rgba(245, 158, 11, 0.05));
    color: #f59e0b;
}

/* Government-Level Professional Search Controls */
.medical-records-search-input {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.875rem;
    color: #374151;
    transition: border-color 0.2s ease;
}

.medical-records-search-input:focus {
    outline: none;
    border-color: #6b7280;
    box-shadow: 0 0 0 1px #6b7280;
}

.medical-records-filter-button {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
}

.medical-records-filter-button:hover {
    border-color: #9ca3af;
    background: #f9fafb;
}

.medical-records-filter-button:focus {
    outline: none;
    border-color: #6b7280;
    box-shadow: 0 0 0 1px #6b7280;
}

/* QR Scanner Button inside Search Bar */
.search-qr-button {
    color: #6b7280;
    cursor: pointer;
    transition: color 0.2s ease;
    padding: 0.5rem;
    border-radius: 3px;
}

.search-qr-button:hover {
    color: #374151;
    background: rgba(243, 244, 246, 0.5);
}

.search-qr-button:focus {
    outline: none;
    color: #1f2937;
    background: rgba(243, 244, 246, 0.8);
}

/* Professional medical records table */
.medical-records-table-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.medical-records-table-header {
    background: #f9fafb;
    border-bottom: 2px solid #e5e7eb;
    padding: 1rem 1.5rem;
}

.medical-records-table-header h3 {
    color: #111827;
    font-weight: 600;
    font-size: 1rem;
    margin: 0;
}

/* Professional medical records table layout */
.medical-records-table {
    width: 100%;
    border-collapse: collapse;
    background: #ffffff;
}

.medical-records-table thead {
    background: #f3f4f6;
    border-bottom: 2px solid #e5e7eb;
}

.medical-records-table th {
    padding: 1rem 1.25rem;
    text-align: left;
    font-weight: 600;
    color: #374151;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    border-right: 1px solid #f3f4f6;
    white-space: nowrap;
}

.medical-records-table th:last-child {
    border-right: none;
}

.medical-records-table tbody tr {
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    transition: background-color 0.2s ease;
}

.medical-records-table tbody tr:nth-child(even) {
    background: #fafafa;
}

.medical-records-table tbody tr:hover {
    background: #f3f4f6;
}

.medical-records-table td {
    padding: 1rem 1.25rem;
    border-right: 1px solid #f9fafb;
    color: #374151;
    font-size: 0.875rem;
    vertical-align: middle;
}

.medical-records-table td:last-child {
    border-right: none;
}

/* Column-specific styling for proper alignment - High Specificity */
table.medical-records-table th.patient-column,
table.medical-records-table td.patient-column {
    width: 16% !important;
    min-width: 160px !important;
    max-width: 180px;
    text-align: left !important;
    vertical-align: middle !important;
}

table.medical-records-table th.date-column,
table.medical-records-table td.date-column {
    width: 11% !important;
    min-width: 110px !important;
    text-align: center !important;
    white-space: nowrap;
    vertical-align: middle !important;
}

table.medical-records-table th.provider-column,
table.medical-records-table td.provider-column {
    width: 14% !important;
    min-width: 130px !important;
    max-width: 150px;
    text-align: left !important;
    vertical-align: middle !important;
}

table.medical-records-table th.type-column,
table.medical-records-table td.type-column {
    width: 10% !important;
    min-width: 90px !important;
    max-width: 110px;
    text-align: center !important;
    vertical-align: middle !important;
}

table.medical-records-table th.diagnosis-column,
table.medical-records-table td.diagnosis-column {
    width: 28% !important;
    min-width: 200px !important;
    max-width: 280px;
    text-align: left !important;
    vertical-align: middle !important;
    word-wrap: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
}

table.medical-records-table th.status-column,
table.medical-records-table td.status-column {
    width: 7% !important;
    min-width: 70px !important;
    max-width: 90px;
    text-align: center !important;
    vertical-align: middle !important;
}

table.medical-records-table th.actions-column,
table.medical-records-table td.actions-column {
    width: 14% !important;
    min-width: 170px !important;
    max-width: 190px;
    text-align: center !important;
    vertical-align: middle !important;
}

/* Government-Level Professional Status Indicators */
.record-status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.record-status-indicator.active {
    background: #22c55e;
}

.record-status-indicator.pending {
    background: #f59e0b;
}

.record-status-indicator.archived {
    background: #6b7280;
}

.record-status-indicator.critical {
    background: #ef4444;
}

/* Professional patient avatar */
.medical-record-avatar {
    width: 40px;
    height: 40px;
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.medical-record-avatar i {
    color: #6b7280;
    font-size: 1rem;
}

/* Patient text container overflow handling */
.medical-records-table .patient-column .min-w-0 {
    max-width: calc(100% - 60px);
}

.medical-records-table .patient-column .text-sm {
    white-space: normal;
    word-wrap: break-word;
    line-height: 1.3;
}

.medical-records-table .patient-column .text-xs {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Government-Level Professional Action Buttons */
.medical-record-action-button {
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: 1px solid #d1d5db;
    background: #ffffff;
    color: #6b7280;
    font-size: 0.875rem;
}

.medical-record-action-button:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
}

.medical-record-action-button.view {
    color: #0ea5e9;
}

.medical-record-action-button.view:hover {
    background: rgba(14, 165, 233, 0.05);
    border-color: rgba(14, 165, 233, 0.2);
}

.medical-record-action-button.edit {
    color: #22c55e;
}

.medical-record-action-button.edit:hover {
    background: rgba(34, 197, 94, 0.05);
    border-color: rgba(34, 197, 94, 0.2);
}

.medical-record-action-button.download {
    color: #8b5cf6;
}

.medical-record-action-button.download:hover {
    background: rgba(139, 92, 246, 0.05);
    border-color: rgba(139, 92, 246, 0.2);
}

.medical-record-action-button.prescription {
    color: #f59e0b;
}

.medical-record-action-button.prescription:hover {
    background: rgba(245, 158, 11, 0.05);
    border-color: rgba(245, 158, 11, 0.2);
}

/* Professional status badges */
.record-status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.75rem;
    display: inline-block;
    border: 1px solid;
}

.record-status-badge.active {
    background: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.record-status-badge.pending {
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.record-status-badge.archived {
    background: rgba(107, 114, 128, 0.1);
    border-color: rgba(107, 114, 128, 0.2);
    color: #6b7280;
}

.record-status-badge.critical {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

/* Professional type badges */
.record-type-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 500;
    font-size: 0.75rem;
    display: inline-block;
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    color: #374151;
}

/* Enhanced New Medical Record Button */
.new-medical-record-button {
    background: linear-gradient(135deg, #1e40af, #1d4ed8);
    border: 1px solid #1e3a8a;
    border-radius: 6px;
    color: white;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 
        0 2px 4px rgba(0, 0, 0, 0.1),
        0 1px 2px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
}

.new-medical-record-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.new-medical-record-button:hover {
    background: linear-gradient(135deg, #1d4ed8, #2563eb);
    border-color: #1e40af;
    transform: translateY(-1px);
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.15),
        0 2px 4px rgba(0, 0, 0, 0.1);
}

.new-medical-record-button:hover::before {
    left: 100%;
}

/* Details row styling */
.medical-records-table .details-row {
    background: #f9fafb !important;
}

.medical-records-table .details-row:hover {
    background: #f3f4f6 !important;
}

.medical-records-table .details-row td {
    border-top: none;
    font-size: 0.75rem;
    color: #6b7280;
    padding: 0.75rem 1.25rem;
}

/* Enhanced table borders and spacing */
.medical-records-table {
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.medical-records-table thead th:first-child {
    border-top-left-radius: 4px;
}

.medical-records-table thead th:last-child {
    border-top-right-radius: 4px;
}

/* Professional table hover effects */
.medical-records-table tbody tr:not(.details-row):hover {
    background: #f8fafc !important;
    box-shadow: inset 0 0 0 1px rgba(14, 165, 233, 0.1);
}

/* Enhanced status indicator positioning */
.record-status-indicator {
    flex-shrink: 0;
}

/* Enhanced table structure for consistent alignment */
.medical-records-table {
    table-layout: fixed !important;
    width: 100% !important;
    border-collapse: separate;
    border-spacing: 0;
    min-width: 1000px;
}

/* Ensure consistent header and data alignment */
.medical-records-table th,
.medical-records-table td {
    box-sizing: border-box !important;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle !important;
    white-space: nowrap;
}

/* Allow text wrapping only for diagnosis column */
.medical-records-table .diagnosis-column {
    white-space: normal !important;
    word-wrap: break-word !important;
    line-height: 1.4;
}

/* Force column widths with higher specificity */
.medical-records-table th.patient-column,
.medical-records-table td.patient-column {
    width: 15% !important;
    min-width: 160px !important;
    max-width: 180px !important;
    text-align: left !important;
    padding-left: 1rem !important;
    overflow: hidden !important;
}

.medical-records-table th.date-column,
.medical-records-table td.date-column {
    width: 12% !important;
    min-width: 120px !important;
    max-width: 140px !important;
    text-align: center !important;
}

.medical-records-table th.provider-column,
.medical-records-table td.provider-column {
    width: 13% !important;
    min-width: 130px !important;
    max-width: 150px !important;
    text-align: center !important;
}

.medical-records-table th.type-column,
.medical-records-table td.type-column {
    width: 9% !important;
    min-width: 80px !important;
    max-width: 100px !important;
    text-align: center !important;
}

.medical-records-table th.diagnosis-column,
.medical-records-table td.diagnosis-column {
    width: 20% !important;
    min-width: 150px !important;
    max-width: 180px !important;
    text-align: left !important;
    padding-left: 1rem !important;
}

.medical-records-table th.status-column,
.medical-records-table td.status-column {
    width: 11% !important;
    min-width: 100px !important;
    max-width: 120px !important;
    text-align: center !important;
}

.medical-records-table th.actions-column,
.medical-records-table td.actions-column {
    width: 20% !important;
    min-width: 180px !important;
    max-width: 200px !important;
    text-align: center !important;
}

/* Enhanced pagination */
.pagination-button {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    transition: all 0.2s ease;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    color: #374151;
}

.pagination-button:hover {
    background: #f9fafb;
    border-color: #9ca3af;
}

/* Priority indicators */
.priority-indicator {
    font-size: 0.75rem;
    font-weight: 600;
}

.priority-indicator.high {
    color: #dc2626;
}

.priority-indicator.urgent {
    color: #991b1b;
}

.priority-indicator.normal {
    color: #6b7280;
}

/* Government-Level Responsive Design */
@media (max-width: 1024px) {
    .medical-records-table .patient-column {
        width: 25%;
        min-width: 200px;
    }

    .medical-records-table .date-column {
        width: 15%;
        min-width: 120px;
    }

    .medical-records-table .provider-column,
    .medical-records-table .type-column {
        width: 14%;
        min-width: 100px;
    }

    .medical-records-table .diagnosis-column {
        width: 20%;
        max-width: 200px;
    }

    .medical-records-table .status-column {
        width: 8%;
        min-width: 80px;
    }

    .medical-records-table .actions-column {
        width: 4%;
        min-width: 140px;
    }
}

@media (max-width: 768px) {
    .medical-records-table {
        font-size: 0.8rem;
    }

    .medical-records-table th,
    .medical-records-table td {
        padding: 0.75rem 0.5rem;
    }

    .medical-records-table .patient-column {
        min-width: 160px;
    }

    .medical-records-table .date-column {
        min-width: 100px;
    }

    .medical-records-table .actions-column {
        min-width: 120px;
    }

    .medical-records-table .diagnosis-column {
        max-width: 150px;
    }

    .medical-record-action-button {
        width: 32px;
        height: 32px;
        font-size: 0.75rem;
    }

    .medical-records-filter-button {
        min-width: 100px;
        font-size: 0.75rem;
    }

    .medical-records-search-input {
        font-size: 0.8rem;
    }
}

@media (max-width: 640px) {
    .stats-card {
        border-radius: 8px;
    }

    .stats-icon {
        width: 40px;
        height: 40px;
    }
}

/* Force table alignment - Override any conflicting styles */
.medical-records-table-card .medical-records-table {
    table-layout: fixed !important;
}

.medical-records-table-card .medical-records-table th,
.medical-records-table-card .medical-records-table td {
    box-sizing: border-box !important;
    vertical-align: middle !important;
}

/* Specific column width enforcement */
.medical-records-table-card .medical-records-table th:nth-child(1),
.medical-records-table-card .medical-records-table td:nth-child(1) {
    width: 15% !important;
    text-align: left !important;
}

.medical-records-table-card .medical-records-table th:nth-child(2),
.medical-records-table-card .medical-records-table td:nth-child(2) {
    width: 12% !important;
    text-align: center !important;
}

.medical-records-table-card .medical-records-table th:nth-child(3),
.medical-records-table-card .medical-records-table td:nth-child(3) {
    width: 13% !important;
    text-align: center !important;
}

.medical-records-table-card .medical-records-table th:nth-child(4),
.medical-records-table-card .medical-records-table td:nth-child(4) {
    width: 9% !important;
    text-align: center !important;
}

.medical-records-table-card .medical-records-table th:nth-child(5),
.medical-records-table-card .medical-records-table td:nth-child(5) {
    width: 20% !important;
    text-align: left !important;
}

.medical-records-table-card .medical-records-table th:nth-child(6),
.medical-records-table-card .medical-records-table td:nth-child(6) {
    width: 11% !important;
    text-align: center !important;
}

.medical-records-table-card .medical-records-table th:nth-child(7),
.medical-records-table-card .medical-records-table td:nth-child(7) {
    width: 20% !important;
    text-align: center !important;
}

/* QR Code Scanner Modal Styles */
.qr-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.qr-modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.qr-modal-content {
    background: white;
    border-radius: 12px;
    padding: 24px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease;
}

#qr-video {
    width: 100%;
    height: 300px;
    border-radius: 8px;
    background: #000;
    object-fit: cover;
}

#medical-records-qr-video {
    width: 100%;
    height: 300px;
    border-radius: 8px;
    background: #000;
    object-fit: cover;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
