/* Professional Appointments Page Styles for ZimHealth-ID */

/* Government-grade appointments layout */
.appointments-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    position: relative;
}

/* Sophisticated background pattern */
.appointments-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.02) 0%, transparent 50%);
    background-size: 1200px 1200px, 800px 800px;
    pointer-events: none;
    z-index: 0;
}

/* Enhanced appointments header styling */
.appointments-header {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.2);
    position: relative;
    z-index: 10;
}

/* Professional stats cards */
.stats-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    box-shadow: 
        0 8px 25px rgba(14, 165, 233, 0.06),
        0 4px 12px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 12px 30px rgba(14, 165, 233, 0.08),
        0 6px 16px rgba(0, 0, 0, 0.06);
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.stats-icon.today {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(34, 197, 94, 0.05));
    color: #22c55e;
}

.stats-icon.pending {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.15), rgba(14, 165, 233, 0.05));
    color: #0ea5e9;
}

.stats-icon.completed {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(34, 197, 94, 0.05));
    color: #22c55e;
}

.stats-icon.cancelled {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(239, 68, 68, 0.05));
    color: #ef4444;
}



/* QR Scanner Button inside Search Bar */
.search-qr-button {
    color: #6b7280;
    cursor: pointer;
    transition: color 0.2s ease;
    padding: 0.5rem;
    border-radius: 3px;
}

.search-qr-button:hover {
    color: #374151;
    background: rgba(243, 244, 246, 0.5);
}

.search-qr-button:focus {
    outline: none;
    color: #1f2937;
    background: rgba(243, 244, 246, 0.8);
}

/* Professional appointments table */
.appointments-table-card {
    background: #ffffff;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.appointments-table-header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 3px solid #cbd5e1;
    padding: 1.25rem 1.5rem;
    position: relative;
}

.appointments-table-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #94a3b8, transparent);
}

.appointments-table-header h3 {
    color: #0f172a;
    font-weight: 700;
    font-size: 1.125rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.appointments-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: #ffffff;
}

.appointments-table thead {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border-bottom: 3px solid #cbd5e1;
}

.appointments-table th {
    padding: 1rem 1.25rem;
    text-align: left;
    font-weight: 700;
    color: #1e293b;
    font-size: 0.8rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    border-right: 2px solid #e2e8f0;
    border-bottom: 2px solid #cbd5e1;
    white-space: nowrap;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    position: relative;
}

.appointments-table th::after {
    content: '';
    position: absolute;
    right: 0;
    top: 20%;
    bottom: 20%;
    width: 1px;
    background: linear-gradient(180deg, transparent, #94a3b8, transparent);
}

.appointments-table th:last-child {
    border-right: none;
}

.appointments-table th:last-child::after {
    display: none;
}

.appointments-table tbody tr {
    background: #ffffff;
    border-bottom: 2px solid #f1f5f9;
    transition: all 0.2s ease;
    position: relative;
}

.appointments-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.appointments-table tbody tr:hover {
    background: #f8fafc;
    box-shadow: inset 0 0 0 1px #e2e8f0;
    transform: translateY(-1px);
}

.appointments-table td {
    padding: 1rem 1.25rem;
    border-right: 2px solid #f1f5f9;
    color: #374151;
    font-size: 0.875rem;
    vertical-align: middle;
    position: relative;
}

.appointments-table td::after {
    content: '';
    position: absolute;
    right: 0;
    top: 15%;
    bottom: 15%;
    width: 1px;
    background: linear-gradient(180deg, transparent, #cbd5e1, transparent);
}

.appointments-table td:last-child {
    border-right: none;
}

.appointments-table td:last-child::after {
    display: none;
}

/* Column-specific styling for proper alignment */
.appointments-table .patient-column {
    min-width: 200px;
    max-width: 250px;
    text-align: left;
    border-left: 3px solid transparent;
    transition: border-color 0.2s ease;
}

.appointments-table .patient-column:hover {
    border-left-color: #3b82f6;
}

.appointments-table .datetime-column {
    min-width: 120px;
    text-align: center;
    white-space: nowrap;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.02), rgba(59, 130, 246, 0.01));
}

.appointments-table .doctor-column {
    min-width: 150px;
    text-align: center;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.02), rgba(34, 197, 94, 0.01));
}

.appointments-table .type-column {
    min-width: 120px;
    text-align: center;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.02), rgba(245, 158, 11, 0.01));
}

.appointments-table .status-column {
    min-width: 100px;
    text-align: center;
    background: linear-gradient(135deg, rgba(168, 85, 247, 0.02), rgba(168, 85, 247, 0.01));
}

.appointments-table .actions-column {
    min-width: 180px;
    text-align: center;
    background: linear-gradient(135deg, rgba(107, 114, 128, 0.02), rgba(107, 114, 128, 0.01));
    border-right: 3px solid transparent;
}

/* Notes row styling */
.appointments-table .notes-row {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
    border-bottom: 2px solid #e2e8f0 !important;
}

.appointments-table .notes-row:hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
    box-shadow: inset 0 0 0 1px #cbd5e1;
}

.appointments-table .notes-row td {
    border-top: 1px solid #e2e8f0;
    border-right: 2px solid #e2e8f0;
    font-size: 0.75rem;
    color: #64748b;
    padding: 0.75rem 1.25rem;
    font-style: italic;
}

.appointments-table .notes-row td:last-child {
    border-right: none;
}

/* Enhanced table borders and shadows */
.appointments-table tbody tr:first-child td {
    border-top: 2px solid #e2e8f0;
}

.appointments-table tbody tr:last-child {
    border-bottom: 3px solid #cbd5e1;
}

.appointments-table tbody tr:last-child td {
    border-bottom: 2px solid #cbd5e1;
}

/* Responsive table adjustments */
@media (max-width: 1024px) {
    .appointments-table .doctor-column,
    .appointments-table .type-column {
        min-width: 100px;
    }

    .appointments-table .actions-column {
        min-width: 140px;
    }

    .appointments-table th,
    .appointments-table td {
        border-right: 1px solid #e2e8f0;
    }
}

@media (max-width: 768px) {
    .appointments-table {
        font-size: 0.8rem;
        border-collapse: collapse;
    }

    .appointments-table th,
    .appointments-table td {
        padding: 0.75rem 0.5rem;
        border-right: 1px solid #e2e8f0;
    }

    .appointments-table .patient-column {
        min-width: 160px;
    }

    .appointments-table .datetime-column {
        min-width: 100px;
    }

    .appointments-table .actions-column {
        min-width: 120px;
    }

    .appointments-table th::after,
    .appointments-table td::after {
        display: none;
    }
}

/* Government-Level Professional Status Indicators */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-indicator.scheduled {
    background: #0ea5e9;
}

.status-indicator.completed {
    background: #22c55e;
}

.status-indicator.cancelled {
    background: #ef4444;
}

.status-indicator.no-show {
    background: #f59e0b;
}

/* Professional patient avatar */
.appointment-avatar {
    width: 48px;
    height: 48px;
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.appointment-avatar i {
    color: #6b7280;
    font-size: 1.125rem;
}

/* Government-Level Professional Action Buttons - Medical Records Style */
.appointment-action-button {
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: 1px solid #d1d5db;
    background: #ffffff;
    color: #6b7280;
    font-size: 0.875rem;
    cursor: pointer;
}

.appointment-action-button:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
}

.appointment-action-button.complete {
    color: #22c55e;
}

.appointment-action-button.complete:hover {
    background: rgba(34, 197, 94, 0.05);
    border-color: rgba(34, 197, 94, 0.2);
}

.appointment-action-button.reschedule {
    color: #0ea5e9;
}

.appointment-action-button.reschedule:hover {
    background: rgba(14, 165, 233, 0.05);
    border-color: rgba(14, 165, 233, 0.2);
}

.appointment-action-button.cancel {
    color: #ef4444;
}

.appointment-action-button.cancel:hover {
    background: rgba(239, 68, 68, 0.05);
    border-color: rgba(239, 68, 68, 0.2);
}

.appointment-action-button:not(.complete):not(.reschedule):not(.cancel) {
    color: #6b7280;
}

.appointment-action-button:not(.complete):not(.reschedule):not(.cancel):hover {
    color: #374151;
}

/* Professional status badges - Medical Records Style */
.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.75rem;
    display: inline-block;
    border: 1px solid;
}

.status-badge.scheduled {
    background: rgba(14, 165, 233, 0.1);
    border-color: rgba(14, 165, 233, 0.2);
    color: #0ea5e9;
}

.status-badge.completed {
    background: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.status-badge.cancelled {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.status-badge.no_show {
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.status-badge.rescheduled {
    background: rgba(107, 114, 128, 0.1);
    border-color: rgba(107, 114, 128, 0.2);
    color: #6b7280;
}

/* Professional type badges - Medical Records Style */
.type-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 500;
    font-size: 0.75rem;
    display: inline-block;
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    color: #374151;
    text-transform: capitalize;
}

/* Specific type badge colors */
.type-badge[data-type="consultation"] {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.12), rgba(14, 165, 233, 0.04));
    border-color: rgba(14, 165, 233, 0.25);
    color: #0369a1;
}

.type-badge[data-type="follow_up"] {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.12), rgba(34, 197, 94, 0.04));
    border-color: rgba(34, 197, 94, 0.25);
    color: #166534;
}

.type-badge[data-type="check_up"] {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.12), rgba(245, 158, 11, 0.04));
    border-color: rgba(245, 158, 11, 0.25);
    color: #92400e;
}

.type-badge[data-type="screening"] {
    background: linear-gradient(135deg, rgba(168, 85, 247, 0.12), rgba(168, 85, 247, 0.04));
    border-color: rgba(168, 85, 247, 0.25);
    color: #7c2d12;
}

.type-badge[data-type="vaccination"] {
    background: linear-gradient(135deg, rgba(236, 72, 153, 0.12), rgba(236, 72, 153, 0.04));
    border-color: rgba(236, 72, 153, 0.25);
    color: #be185d;
}

.type-badge[data-type="emergency"] {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.12), rgba(239, 68, 68, 0.04));
    border-color: rgba(239, 68, 68, 0.25);
    color: #991b1b;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }
    50% {
        box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);
    }
}

/* Enhanced Schedule Appointment Button */
.schedule-appointment-button {
    background: linear-gradient(135deg, #1e40af, #1d4ed8);
    border: 1px solid #1e3a8a;
    border-radius: 6px;
    color: white;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 
        0 2px 4px rgba(0, 0, 0, 0.1),
        0 1px 2px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
}

.schedule-appointment-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.schedule-appointment-button:hover {
    background: linear-gradient(135deg, #1d4ed8, #2563eb);
    border-color: #1e40af;
    transform: translateY(-1px);
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.15),
        0 2px 4px rgba(0, 0, 0, 0.1);
}

.schedule-appointment-button:hover::before {
    left: 100%;
}

/* Enhanced pagination */
.pagination-button {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    transition: all 0.2s ease;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    color: #374151;
}

.pagination-button:hover {
    background: #f9fafb;
    border-color: #9ca3af;
}

/* Government-Level Responsive Design */
@media (max-width: 768px) {
    .patients-table-card {
        border-radius: 4px;
        margin: 0 0.5rem;
    }
    
    .appointment-card {
        padding: 1rem;
    }
    
    .appointment-avatar {
        width: 40px;
        height: 40px;
    }
    
    .appointment-action-button {
        width: 32px;
        height: 32px;
        font-size: 0.75rem;
    }
}

@media (max-width: 640px) {
    .stats-card {
        border-radius: 8px;
    }
    
    .stats-icon {
        width: 40px;
        height: 40px;
    }
}

/* View toggle buttons */
.view-toggle-button {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1px solid #d1d5db;
    background: #ffffff;
    color: #6b7280;
}

.view-toggle-button.active {
    background: #1e40af;
    border-color: #1e3a8a;
    color: white;
}

.view-toggle-button:hover:not(.active) {
    background: #f9fafb;
    border-color: #9ca3af;
}

/* Professional priority indicators */
.priority-indicator {
    font-size: 0.75rem;
    font-weight: 600;
    color: #ef4444;
}

.priority-indicator.high {
    color: #dc2626;
}

.priority-indicator.urgent {
    color: #991b1b;
}

/* AJAX Pagination Styles */
.pagination-container {
    background: #f9fafb;
    border-top: 1px solid #e5e7eb;
}

.pagination-btn {
    background: #ffffff;
    border: 1px solid #d1d5db;
    color: #374151;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    cursor: pointer;
}

.pagination-btn:hover:not(.disabled) {
    background: #f3f4f6;
    border-color: #9ca3af;
}

.pagination-btn.disabled {
    background: #f9fafb;
    color: #9ca3af;
    cursor: not-allowed;
    opacity: 0.6;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Appointment Status Badges */
.appointment-status-badge.scheduled {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #3b82f6;
}

.appointment-status-badge.completed {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #22c55e;
}

.appointment-status-badge.cancelled {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #ef4444;
}

.appointment-status-badge.no-show {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #f59e0b;
}

.appointment-status-badge.rescheduled {
    background: #f3e8ff;
    color: #7c2d12;
    border: 1px solid #a855f7;
}

/* Appointment Type Badges */
.appointment-type-badge.consultation {
    background: #e0f2fe;
    color: #0c4a6e;
    border: 1px solid #0ea5e9;
}

.appointment-type-badge.follow-up {
    background: #f0fdf4;
    color: #065f46;
    border: 1px solid #10b981;
}

.appointment-type-badge.check-up {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #f59e0b;
}

.appointment-type-badge.vaccination {
    background: #f3e8ff;
    color: #7c2d12;
    border: 1px solid #a855f7;
}

.appointment-type-badge.screening {
    background: #fce7f3;
    color: #be185d;
    border: 1px solid #ec4899;
}

.appointment-type-badge.emergency {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #ef4444;
}

/* Action Button Enhancements */
.action-button.reschedule {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #f59e0b;
}

.action-button.reschedule:hover {
    background: #fde68a;
    border-color: #d97706;
}

.action-button.complete {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #22c55e;
}

.action-button.complete:hover {
    background: #bbf7d0;
    border-color: #16a34a;
}

.action-button.cancel {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #ef4444;
}

.action-button.cancel:hover {
    background: #fecaca;
    border-color: #dc2626;
}

/* Notification Styles for AJAX Operations */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 400px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success-notification {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #166534;
}

.notification.error-notification {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #991b1b;
}

.notification.info-notification {
    background: #eff6ff;
    border: 1px solid #bfdbfe;
    color: #1e40af;
}

.notification-content {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    font-weight: 500;
}

/* QR Code Scanner Modal Styles */
.qr-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.qr-modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.qr-modal-content {
    background: white;
    border-radius: 12px;
    padding: 24px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease;
}

#qr-video {
    width: 100%;
    height: 300px;
    border-radius: 8px;
    background: #000;
    object-fit: cover;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
