/* Prescription Status Badge Styles */
.status-badge-active {
    background-color: #10b981;
    color: white;
}

.status-badge-completed {
    background-color: #3b82f6;
    color: white;
}

.status-badge-discontinued {
    background-color: #ef4444;
    color: white;
}

.status-badge-on_hold {
    background-color: #f59e0b;
    color: white;
}

/* Prescription detail specific styles */
.prescription-medication-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e40af;
}

.prescription-dosage-highlight {
    background-color: #dbeafe;
    border: 1px solid #93c5fd;
    border-radius: 0.375rem;
    padding: 0.5rem;
    font-weight: 500;
    color: #1e40af;
}

.prescription-instructions-box {
    background-color: #eff6ff;
    border: 1px solid #bfdbfe;
    border-radius: 0.5rem;
    padding: 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
}

.prescription-timeline-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.5rem 0;
}

.prescription-timeline-marker {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    flex-shrink: 0;
    margin-top: 0.125rem;
}

.prescription-timeline-marker.active {
    background-color: #3b82f6;
}

.prescription-timeline-marker.completed {
    background-color: #10b981;
}

.prescription-action-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s;
    text-decoration: none;
}

.prescription-action-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.prescription-action-button.success {
    background-color: #10b981;
    color: white;
    border: 1px solid #10b981;
}

.prescription-action-button.success:hover {
    background-color: #059669;
    border-color: #059669;
}

.prescription-action-button.warning {
    background-color: #f59e0b;
    color: white;
    border: 1px solid #f59e0b;
}

.prescription-action-button.warning:hover {
    background-color: #d97706;
    border-color: #d97706;
}

.prescription-action-button.danger {
    background-color: #ef4444;
    color: white;
    border: 1px solid #ef4444;
}

.prescription-action-button.danger:hover {
    background-color: #dc2626;
    border-color: #dc2626;
}

/* Print styles */
@media print {
    .prescription-action-button,
    .patients-table-header,
    .government-filter-button {
        display: none !important;
    }
    
    .patients-table-card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        margin-bottom: 1rem !important;
    }
    
    .patients-table {
        font-size: 12px !important;
    }
    
    .prescription-medication-name {
        font-size: 18px !important;
        font-weight: bold !important;
    }
}