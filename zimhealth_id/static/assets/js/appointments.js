/**
 * Professional Appointments Page JavaScript for ZimHealth-ID
 * Handles search, filtering, and interactive features
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize appointments page functionality
    initAppointmentsSearch();
    initQRCodeScanner();
    initFilterFunctionality();
    initViewToggle();
    initActionButtons();
    initScrollAnimations();
    // Remove initDemoData() as we're using real-time data
});

/**
 * Initialize enhanced appointment search functionality with real-time AJAX
 */
function initAppointmentsSearch() {
    const searchInput = document.getElementById('appointment-search');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function(e) {
            performRealTimeFilter();
        }, 300));
    }
}

/**
 * Initialize QR Code Scanner functionality
 */
function initQRCodeScanner() {
    const qrButton = document.getElementById('qr-scanner-btn');
    const qrModal = document.getElementById('qr-modal');
    const closeQRBtn = document.getElementById('close-qr-btn');
    
    if (qrButton && qrModal) {
        qrButton.addEventListener('click', openQRScanner);
        closeQRBtn?.addEventListener('click', closeQRScanner);
        qrModal.addEventListener('click', function(e) {
            if (e.target === qrModal) closeQRScanner();
        });
    }
}

/**
 * Open QR Code Scanner
 */
async function openQRScanner() {
    const qrModal = document.getElementById('qr-modal');
    const qrVideo = document.getElementById('qr-video');

    try {
        const stream = await navigator.mediaDevices.getUserMedia({
            video: {
                facingMode: 'environment',
                width: { ideal: 1280 },
                height: { ideal: 720 }
            }
        });

        qrVideo.srcObject = stream;
        qrModal.classList.add('active');

        // Update modal content to show scanning status
        updateQRScannerStatus('Scanning for QR codes...', 'info');

        showNotification('QR Scanner activated. Point camera at patient QR code.', 'info');

        // Start QR code detection
        startQRDetection(qrVideo);

    } catch (error) {
        console.error('Error accessing camera:', error);
        showNotification('Camera access denied. Please enable camera permissions.', 'error');
    }
}

/**
 * Start QR Code Detection using jsQR library
 */
function startQRDetection(video) {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    let isScanning = true;

    function scanFrame() {
        if (!isScanning) return;

        const qrModal = document.getElementById('qr-modal');
        if (!qrModal || !qrModal.classList.contains('active')) {
            isScanning = false;
            return;
        }

        if (video.readyState === video.HAVE_ENOUGH_DATA) {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0, canvas.width, canvas.height);

            const imageData = context.getImageData(0, 0, canvas.width, canvas.height);

            // Use jsQR library to detect QR codes
            if (typeof jsQR !== 'undefined') {
                const qrCode = jsQR(imageData.data, imageData.width, imageData.height, {
                    inversionAttempts: "dontInvert",
                });

                if (qrCode) {
                    console.log('QR Code detected:', qrCode.data);
                    updateQRScannerStatus('QR Code detected! Processing...', 'success');
                    isScanning = false;
                    handleQRCodeDetected(qrCode.data);
                    return;
                }

                // Update status to show active scanning
                updateQRScannerStatus('Scanning... Point camera at QR code', 'info');
            } else {
                // Fallback to mock detection if jsQR is not loaded
                console.warn('jsQR library not loaded, using fallback detection');
                updateQRScannerStatus('Demo mode: Simulating QR detection...', 'warning');
                const mockResult = detectQRPatternFallback();
                if (mockResult) {
                    updateQRScannerStatus('Demo QR Code detected!', 'success');
                    isScanning = false;
                    handleQRCodeDetected(mockResult);
                    return;
                }
            }
        }

        // Continue scanning
        requestAnimationFrame(scanFrame);
    }

    // Start scanning when video is ready
    video.addEventListener('loadedmetadata', () => {
        scanFrame();
    });

    // If video is already loaded, start immediately
    if (video.readyState >= video.HAVE_METADATA) {
        scanFrame();
    }

    // Stop scanning when modal is closed
    const qrModal = document.getElementById('qr-modal');
    if (qrModal) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (!qrModal.classList.contains('active')) {
                        isScanning = false;
                    }
                }
            });
        });
        observer.observe(qrModal, { attributes: true });
    }
}

/**
 * Handle QR Code Detection
 */
async function handleQRCodeDetected(qrData) {
    closeQRScanner();

    // Extract ZimHealth ID from QR data
    let zimhealthId = extractZimHealthId(qrData);

    if (!zimhealthId) {
        showNotification('Invalid QR code format. Please scan a valid patient QR code.', 'error');
        return;
    }

    // Show loading notification
    showNotification('Looking up patient appointments...', 'info');

    try {
        // Try to find patient via AJAX
        const response = await fetch(`/api/ajax/scan-qr/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': getCsrfToken(),
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: `qr_data=${encodeURIComponent(zimhealthId)}`
        });

        const data = await response.json();

        if (data.success) {
            // Patient found - update search and filter to show only this patient's appointments
            const searchInput = document.getElementById('appointment-search');
            if (searchInput) {
                // Set the search input to the ZimHealth ID
                searchInput.value = zimhealthId;

                // Clear other filters to ensure only search is active
                clearAllFilters();

                // Perform real-time filter to show only the scanned patient's appointments
                performRealTimeFilter();

                // Highlight the patient appointments after filtering
                setTimeout(() => {
                    highlightPatient(zimhealthId);
                }, 500);
            }

            showNotification(`Found appointments for: ${data.patient.full_name} (${zimhealthId})`, 'success');

        } else {
            showNotification(data.error || 'No appointments found for this patient.', 'error');
        }

    } catch (error) {
        console.error('QR lookup error:', error);
        showNotification('Error looking up patient appointments. Please try again.', 'error');
    }
}

/**
 * Close QR Code Scanner
 */
function closeQRScanner() {
    const qrModal = document.getElementById('qr-modal');
    const qrVideo = document.getElementById('qr-video');
    
    if (qrVideo.srcObject) {
        const tracks = qrVideo.srcObject.getTracks();
        tracks.forEach(track => track.stop());
        qrVideo.srcObject = null;
    }
    
    qrModal.classList.remove('active');
}

/**
 * Extract ZimHealth ID from QR code data
 */
function extractZimHealthId(qrData) {
    // Handle different QR code formats
    if (typeof qrData === 'string') {
        // Direct ZimHealth ID format: ZH-YYYY-XXXXXX
        if (qrData.match(/^ZH-\d{4}-\d{6}$/)) {
            return qrData;
        }

        // Multi-line format with ZimHealth-ID prefix
        const lines = qrData.split('\n');
        for (const line of lines) {
            if (line.startsWith('ZimHealth-ID:')) {
                const id = line.replace('ZimHealth-ID:', '').trim();
                if (id.match(/^ZH-\d{4}-\d{6}$/)) {
                    return id;
                }
            }
        }

        // Try to find ZH-YYYY-XXXXXX pattern anywhere in the string
        const match = qrData.match(/ZH-\d{4}-\d{6}/);
        if (match) {
            return match[0];
        }
    }

    return null;
}

/**
 * Clear all filter inputs except search
 */
function clearAllFilters() {
    const statusFilter = document.getElementById('status-filter');
    const typeFilter = document.getElementById('type-filter');
    const dateFilter = document.getElementById('date-filter');

    if (statusFilter) statusFilter.value = '';
    if (typeFilter) typeFilter.value = '';
    if (dateFilter) dateFilter.value = '';
}

/**
 * Update QR scanner status message
 */
function updateQRScannerStatus(message, type = 'info') {
    const statusElement = document.querySelector('#qr-modal .text-sm.text-gray-600');
    if (statusElement) {
        statusElement.textContent = message;

        // Update color based on type
        statusElement.className = `text-sm ${
            type === 'success' ? 'text-green-600' :
            type === 'error' ? 'text-red-600' :
            type === 'warning' ? 'text-yellow-600' :
            'text-gray-600'
        }`;
    }
}

/**
 * Highlight found patient in table
 */
function highlightPatient(patientId) {
    const rows = document.querySelectorAll('.appointments-table tbody tr');

    rows.forEach(row => {
        const patientIdElement = row.querySelector('.patient-column .text-xs');
        if (patientIdElement && patientIdElement.textContent.trim() === patientId) {
            // Enhanced highlighting for QR scan results
            row.style.background = 'linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(34, 197, 94, 0.08))';
            row.style.border = '2px solid rgba(34, 197, 94, 0.5)';
            row.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.2)';
            row.style.transform = 'scale(1.02)';
            row.style.transition = 'all 0.3s ease';

            // Scroll to the highlighted appointment
            row.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // Add a QR scan indicator
            const qrIndicator = document.createElement('div');
            qrIndicator.className = 'qr-scan-indicator';
            qrIndicator.innerHTML = '<i class="fas fa-qrcode"></i> Scanned';
            qrIndicator.style.cssText = `
                position: absolute;
                top: -10px;
                right: 10px;
                background: #22c55e;
                color: white;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 10px;
                font-weight: bold;
                z-index: 10;
                animation: pulse 2s infinite;
            `;

            row.style.position = 'relative';
            row.appendChild(qrIndicator);

            // Remove highlighting after 5 seconds
            setTimeout(() => {
                row.style.background = '';
                row.style.border = '';
                row.style.boxShadow = '';
                row.style.transform = '';
                if (qrIndicator.parentNode) {
                    qrIndicator.remove();
                }
            }, 5000);
        }
    });
}

/**
 * Fallback QR pattern detection for demo purposes
 */
function detectQRPatternFallback() {
    // For demo purposes, simulate finding a QR code after a few seconds
    const now = Date.now();
    const scanStartTime = window.qrScanStartTime || now;

    if (!window.qrScanStartTime) {
        window.qrScanStartTime = now;
    }

    // Simulate detection after 3-5 seconds
    if (now - scanStartTime > 3000 && Math.random() < 0.3) {
        // Return a mock ZimHealth ID from existing patients
        const mockIds = ['ZH-***********', 'ZH-***********', 'ZH-***********'];
        window.qrScanStartTime = null; // Reset for next scan
        return mockIds[Math.floor(Math.random() * mockIds.length)];
    }

    return null;
}

/**
 * Get CSRF token for AJAX requests
 */
function getCsrfToken() {
    const csrfInput = document.querySelector('[name=csrfmiddlewaretoken]');
    return csrfInput ? csrfInput.value : '';
}



/**
 * Initialize filter functionality for all filter controls
 */
function initFilterFunctionality() {
    // Status filter
    const statusFilter = document.getElementById('status-filter');
    if (statusFilter) {
        statusFilter.addEventListener('change', performRealTimeFilter);
    }

    // Type filter
    const typeFilter = document.getElementById('type-filter');
    if (typeFilter) {
        typeFilter.addEventListener('change', performRealTimeFilter);
    }

    // Date filter
    const dateFilter = document.getElementById('date-filter');
    if (dateFilter) {
        dateFilter.addEventListener('change', performRealTimeFilter);
    }

    // Initialize with current data
    performRealTimeFilter();
}

/**
 * Apply filters to appointment list
 */
function applyFilters() {
    const statusFilter = document.getElementById('status-filter')?.value || '';
    const typeFilter = document.getElementById('type-filter')?.value || '';
    const dateFilter = document.getElementById('date-filter')?.value || '';
    const searchTerm = document.getElementById('appointment-search')?.value.toLowerCase() || '';

    const appointments = document.querySelectorAll('.appointments-table tbody tr:not(.notes-row)');
    let visibleCount = 0;

    appointments.forEach(appointment => {
        const appointmentText = appointment.textContent.toLowerCase();
        const statusText = appointment.querySelector('.status-badge')?.textContent.toLowerCase() || '';
        const typeText = appointment.querySelector('.type-badge')?.textContent.toLowerCase() || '';
        const dateText = appointment.querySelector('.appointment-date')?.textContent || '';

        const matchesSearch = !searchTerm || appointmentText.includes(searchTerm);
        const matchesStatus = !statusFilter || statusText.includes(statusFilter.toLowerCase());
        const matchesType = !typeFilter || typeText.includes(typeFilter.toLowerCase());
        const matchesDate = !dateFilter || dateText.includes(dateFilter);

        const isVisible = matchesSearch && matchesStatus && matchesType && matchesDate;

        // Handle notes row visibility
        const nextRow = appointment.nextElementSibling;
        const isNotesRow = nextRow && nextRow.classList.contains('notes-row');

        if (isVisible) {
            appointment.style.display = '';
            if (isNotesRow) nextRow.style.display = '';
            visibleCount++;
        } else {
            appointment.style.display = 'none';
            if (isNotesRow) nextRow.style.display = 'none';
        }
    });

    updateResultsCount(visibleCount);
}

/**
 * Initialize view toggle functionality
 */
function initViewToggle() {
    const listViewBtn = document.getElementById('list-view-btn');
    const calendarViewBtn = document.getElementById('calendar-view-btn');
    
    if (listViewBtn && calendarViewBtn) {
        listViewBtn.addEventListener('click', function() {
            setActiveView('list');
        });
        
        calendarViewBtn.addEventListener('click', function() {
            setActiveView('calendar');
        });
    }
}

/**
 * Set active view (list or calendar)
 */
function setActiveView(view) {
    const listViewBtn = document.getElementById('list-view-btn');
    const calendarViewBtn = document.getElementById('calendar-view-btn');
    const appointmentsList = document.querySelector('.appointments-list');
    const calendarView = document.querySelector('.calendar-view');
    
    if (view === 'list') {
        listViewBtn?.classList.add('active');
        calendarViewBtn?.classList.remove('active');
        if (appointmentsList) appointmentsList.style.display = 'block';
        if (calendarView) calendarView.style.display = 'none';
    } else {
        calendarViewBtn?.classList.add('active');
        listViewBtn?.classList.remove('active');
        if (appointmentsList) appointmentsList.style.display = 'none';
        if (calendarView) calendarView.style.display = 'block';
    }
}

/**
 * Initialize action buttons
 */
function initActionButtons() {
    const actionButtons = document.querySelectorAll('.appointment-action-button');
    
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
            
            // Handle different actions
            const action = this.getAttribute('data-action');
            const appointmentId = this.getAttribute('data-appointment-id');
            
            switch(action) {
                case 'complete':
                    handleCompleteAppointment(appointmentId);
                    break;
                case 'reschedule':
                    handleRescheduleAppointment(appointmentId);
                    break;
                case 'cancel':
                    handleCancelAppointment(appointmentId);
                    break;
                case 'view':
                    handleViewAppointment(appointmentId);
                    break;
                case 'edit':
                    handleEditAppointment(appointmentId);
                    break;
            }
        });
    });
}

/**
 * Handle appointment actions
 */
function handleCompleteAppointment(appointmentId) {
    showNotification('Appointment marked as completed', 'success');
}

function handleRescheduleAppointment(appointmentId) {
    showNotification('Reschedule functionality coming soon', 'info');
}

function handleCancelAppointment(appointmentId) {
    if (confirm('Are you sure you want to cancel this appointment?')) {
        showNotification('Appointment cancelled', 'warning');
    }
}

function handleViewAppointment(appointmentId) {
    showNotification('Opening appointment details', 'info');
}

function handleEditAppointment(appointmentId) {
    showNotification('Opening appointment editor', 'info');
}

/**
 * Update results count display
 */
function updateResultsCount(count) {
    const countElement = document.querySelector('.results-count');
    if (countElement) {
        countElement.textContent = `${count} appointments`;
    }
}

/**
 * Initialize scroll animations
 */
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observe cards and appointment items
    document.querySelectorAll('.stats-card, .appointments-table-card').forEach(element => {
        element.classList.add('animate-on-scroll');
        observer.observe(element);
    });
}

/**
 * Initialize demo data if no appointments exist
 */
function initDemoData() {
    const emptyState = document.querySelector('td[colspan="6"]');
    const appointmentsTBody = document.querySelector('.appointments-table tbody');

    if (emptyState && appointmentsTBody) {
        // Create demo appointments
        const demoAppointments = [
            {
                id: 'APT-001',
                patientName: 'Sarah Johnson',
                patientId: 'ZH-2024-001',
                date: 'Jul 29, 2025',
                time: '09:00',
                doctor: 'Dr. Smith',
                facility: 'Central Hospital',
                type: 'Consultation',
                status: 'scheduled',
                priority: 'normal',
                reason: 'Regular checkup and blood pressure monitoring'
            },
            {
                id: 'APT-002',
                patientName: 'Michael Brown',
                patientId: 'ZH-2024-002',
                date: 'Jul 29, 2025',
                time: '10:30',
                doctor: 'Dr. Wilson',
                facility: 'Medical Center',
                type: 'Follow-up',
                status: 'completed',
                priority: 'normal',
                reason: 'Post-surgery follow-up examination'
            },
            {
                id: 'APT-003',
                patientName: 'Emma Wilson',
                patientId: 'ZH-2024-003',
                date: 'Jul 30, 2025',
                time: '14:00',
                doctor: 'Dr. Martinez',
                facility: 'Health Clinic',
                type: 'Screening',
                status: 'scheduled',
                priority: 'high',
                reason: 'Annual health screening and vaccination'
            }
        ];
        
        // Replace empty state with demo appointments
        appointmentsTBody.innerHTML = demoAppointments.map(appointment =>
            createAppointmentTableRow(appointment)
        ).join('');

        // Update results count
        updateResultsCount(demoAppointments.length);

        // Re-initialize interactions
        initActionButtons();
    }
}

/**
 * Create appointment table row HTML
 */
function createAppointmentTableRow(appointment) {
    return `
        <tr>
            <!-- Patient Column -->
            <td class="patient-column">
                <div class="flex items-center space-x-3">
                    <div class="status-indicator ${appointment.status}"></div>
                    <div class="appointment-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div>
                        <div class="text-sm font-semibold text-gray-900">${appointment.patientName}</div>
                        <div class="text-xs text-gray-500">${appointment.patientId}</div>
                    </div>
                </div>
            </td>

            <!-- Date & Time Column -->
            <td class="datetime-column">
                <div class="text-sm font-medium text-gray-900 appointment-date">${appointment.date}</div>
                <div class="text-xs text-gray-500">${appointment.time}</div>
            </td>

            <!-- Healthcare Provider Column -->
            <td class="doctor-column">
                <div class="text-sm font-medium text-gray-900">${appointment.doctor}</div>
                <div class="text-xs text-gray-500">${appointment.facility}</div>
            </td>

            <!-- Type Column -->
            <td class="type-column">
                <span class="type-badge">${appointment.type}</span>
                ${appointment.priority !== 'normal' ? `<div class="priority-indicator ${appointment.priority} mt-1 text-xs">${appointment.priority.toUpperCase()}</div>` : ''}
            </td>

            <!-- Status Column -->
            <td class="status-column">
                <span class="status-badge ${appointment.status}">${appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}</span>
            </td>

            <!-- Actions Column -->
            <td class="actions-column">
                <div class="flex items-center justify-center space-x-1">
                    ${appointment.status === 'scheduled' ? `
                        <button class="appointment-action-button complete" data-action="complete" data-appointment-id="${appointment.id}" title="Mark Complete">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="appointment-action-button reschedule" data-action="reschedule" data-appointment-id="${appointment.id}" title="Reschedule">
                            <i class="fas fa-calendar-alt"></i>
                        </button>
                        <button class="appointment-action-button cancel" data-action="cancel" data-appointment-id="${appointment.id}" title="Cancel">
                            <i class="fas fa-times"></i>
                        </button>
                    ` : ''}
                    <button class="appointment-action-button" data-action="view" data-appointment-id="${appointment.id}" title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="appointment-action-button" data-action="edit" data-appointment-id="${appointment.id}" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
            </td>
        </tr>
        ${appointment.reason ? `
            <tr class="notes-row">
                <td colspan="6" class="px-6 py-2 bg-gray-50 border-b border-gray-200">
                    <p class="text-xs text-gray-600"><strong>Reason:</strong> ${appointment.reason}</p>
                </td>
            </tr>
        ` : ''}
    `;
}

/**
 * Show notification message
 */
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existing = document.querySelector('.appointments-notification');
    if (existing) existing.remove();
    
    const notification = document.createElement('div');
    const typeConfig = {
        success: { bg: 'bg-health-500', icon: 'fa-check-circle' },
        error: { bg: 'bg-red-500', icon: 'fa-exclamation-circle' },
        warning: { bg: 'bg-orange-500', icon: 'fa-exclamation-triangle' },
        info: { bg: 'bg-medical-500', icon: 'fa-info-circle' }
    };
    
    const config = typeConfig[type] || typeConfig.info;
    
    notification.className = `appointments-notification fixed top-20 right-4 z-50 p-4 rounded-xl shadow-2xl max-w-sm transform translate-x-full transition-all duration-500 ${config.bg} text-white backdrop-blur-lg`;
    
    notification.innerHTML = `
        <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
                <i class="fas ${config.icon} text-lg"></i>
            </div>
            <div class="flex-1">
                <p class="font-semibold text-sm">${message}</p>
                <p class="text-xs opacity-90 mt-1">ZimHealth-ID Appointments</p>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 hover:bg-white hover:bg-opacity-20 rounded-full p-1 transition-colors">
                <i class="fas fa-times text-sm"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%) scale(0.9)';
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 500);
    }, 5000);
}

/**
 * Utility function to debounce events
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    .animate-on-scroll {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.6s ease-out;
    }
    
    .animate-on-scroll.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
`;
document.head.appendChild(style);

/**
 * Perform real-time filtering with AJAX
 */
function performRealTimeFilter(page = 1) {
    const searchInput = document.getElementById('appointment-search');
    const statusFilter = document.getElementById('status-filter');
    const typeFilter = document.getElementById('type-filter');
    const dateFilter = document.getElementById('date-filter');

    // Show loading state
    showLoadingState();

    // Collect filter parameters
    const params = new URLSearchParams({
        search: searchInput ? searchInput.value : '',
        status: statusFilter ? statusFilter.value : '',
        type: typeFilter ? typeFilter.value : '',
        date: dateFilter ? dateFilter.value : '',
        page: page
    });

    // Remove empty parameters
    for (let [key, value] of [...params.entries()]) {
        if (!value) {
            params.delete(key);
        }
    }

    // Make AJAX request
    fetch(`/api/ajax/appointments/filter/?${params.toString()}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            updateAppointmentsTable(data.appointments);
            updatePagination(data.pagination);
            updateResultsCount(data.pagination.total_count, data.pagination.start_index, data.pagination.end_index);
            updateStatistics(data.statistics);
            hideLoadingState();
        } else {
            throw new Error(data.error || 'Unknown error occurred');
        }
    })
    .catch(error => {
        console.error('Filter error:', error);
        showErrorState('Failed to load appointments. Please try again.');
        hideLoadingState();
    });
}

/**
 * Update appointments table with new data
 */
function updateAppointmentsTable(appointments) {
    const tbody = document.querySelector('.appointments-table tbody');
    if (!tbody) return;

    if (appointments.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-12">
                    <div class="flex flex-col items-center">
                        <div class="appointment-avatar mx-auto mb-4">
                            <i class="fas fa-calendar-alt text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">No appointments found</h3>
                        <p class="text-gray-500 mb-4">Try adjusting your search criteria or filters.</p>
                        <a href="/api/appointments/new/" class="schedule-appointment-button">
                            <i class="fas fa-calendar-plus mr-2"></i>Schedule Appointment
                        </a>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    const rows = appointments.map(appointment => {
        const statusClass = getStatusClass(appointment.status_code);
        const priorityClass = getPriorityClass(appointment.priority_code);
        const typeClass = getTypeClass(appointment.appointment_type_code);

        return `
            <tr data-appointment-id="${appointment.id}">
                <!-- Patient Column -->
                <td class="patient-column">
                    <div class="flex items-center space-x-3">
                        <div class="status-indicator ${statusClass}"></div>
                        <div class="appointment-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div>
                            <div class="text-sm font-semibold text-gray-900">${appointment.patient_name}</div>
                            <div class="text-xs text-gray-500">${appointment.patient_zimhealth_id}</div>
                        </div>
                    </div>
                </td>

                <!-- Date & Time Column -->
                <td class="datetime-column">
                    <div class="text-sm font-medium text-gray-900 appointment-date">${appointment.date}</div>
                    <div class="text-xs text-gray-500">${appointment.time}</div>
                    <div class="text-xs text-blue-600">${appointment.time_until}</div>
                </td>

                <!-- Healthcare Provider Column -->
                <td class="doctor-column">
                    <div class="text-sm font-medium text-gray-900">${appointment.doctor_name}</div>
                    <div class="text-xs text-gray-500">${appointment.facility_name}</div>
                    ${appointment.department ? `<div class="text-xs text-gray-400">${appointment.department}</div>` : ''}
                </td>

                <!-- Type Column -->
                <td class="type-column">
                    <span class="appointment-type-badge ${typeClass}">${appointment.appointment_type}</span>
                    ${appointment.priority_code !== 'normal' ? `<div class="text-xs text-orange-600 mt-1">${appointment.priority}</div>` : ''}
                </td>

                <!-- Status Column -->
                <td class="status-column">
                    <span class="appointment-status-badge ${statusClass}">${appointment.status}</span>
                    <div class="text-xs text-gray-500 mt-1">${appointment.estimated_duration} min</div>
                </td>

                <!-- Actions Column -->
                <td class="actions-column">
                    <div class="flex items-center space-x-2">
                        <a href="${appointment.detail_url}" class="action-button view" title="View Details">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="${appointment.edit_url}" class="action-button edit" title="Edit Appointment">
                            <i class="fas fa-edit"></i>
                        </a>
                        ${appointment.status_code === 'scheduled' ? `
                            <button onclick="rescheduleAppointment('${appointment.id}', '${appointment.patient_name}')" class="action-button reschedule" title="Reschedule">
                                <i class="fas fa-calendar-alt"></i>
                            </button>
                            <button onclick="updateAppointmentStatus('${appointment.id}', 'completed')" class="action-button complete" title="Mark Complete">
                                <i class="fas fa-check"></i>
                            </button>
                            <button onclick="updateAppointmentStatus('${appointment.id}', 'cancelled')" class="action-button cancel" title="Cancel">
                                <i class="fas fa-times"></i>
                            </button>
                        ` : ''}
                        <button onclick="confirmDeleteAppointment('${appointment.id}', '${appointment.patient_name}', '${appointment.date}', '${appointment.time}')" class="action-button delete" title="Delete Appointment">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');

    tbody.innerHTML = rows;
}

/**
 * Update pagination controls
 */
function updatePagination(pagination) {
    const paginationContainer = document.querySelector('.pagination-container');
    if (!paginationContainer) return;

    if (pagination.total_pages <= 1) {
        paginationContainer.style.display = 'none';
        return;
    }

    paginationContainer.style.display = 'block';

    let paginationHTML = '<div class="flex items-center justify-between">';

    // Previous button
    if (pagination.has_previous) {
        paginationHTML += `<button onclick="performRealTimeFilter(${pagination.current_page - 1})" class="pagination-btn">Previous</button>`;
    } else {
        paginationHTML += '<button class="pagination-btn disabled" disabled>Previous</button>';
    }

    // Page info
    paginationHTML += `<span class="text-sm text-gray-600">Page ${pagination.current_page} of ${pagination.total_pages}</span>`;

    // Next button
    if (pagination.has_next) {
        paginationHTML += `<button onclick="performRealTimeFilter(${pagination.current_page + 1})" class="pagination-btn">Next</button>`;
    } else {
        paginationHTML += '<button class="pagination-btn disabled" disabled>Next</button>';
    }

    paginationHTML += '</div>';
    paginationContainer.innerHTML = paginationHTML;
}

/**
 * Update results count display
 */
function updateResultsCount(totalCount, startIndex = 0, endIndex = 0) {
    const resultsCounts = document.querySelectorAll('.results-count');

    let countText;
    if (totalCount === 0) {
        countText = 'No appointments found';
    } else if (totalCount === 1) {
        countText = '1 appointment found';
    } else if (startIndex && endIndex) {
        countText = `Showing ${startIndex}-${endIndex} of ${totalCount} appointments`;
    } else {
        countText = `${totalCount} appointments found`;
    }

    resultsCounts.forEach(element => {
        element.textContent = countText;
    });
}

/**
 * Update statistics cards
 */
function updateStatistics(stats) {
    // Update statistics cards if they exist
    const todayCard = document.querySelector('.stats-card .stats-icon.today')?.parentElement?.nextElementSibling?.querySelector('p:last-child');
    const pendingCard = document.querySelector('.stats-card .stats-icon.pending')?.parentElement?.nextElementSibling?.querySelector('p:last-child');
    const completedCard = document.querySelector('.stats-card .stats-icon.completed')?.parentElement?.nextElementSibling?.querySelector('p:last-child');
    const cancelledCard = document.querySelector('.stats-card .stats-icon.cancelled')?.parentElement?.nextElementSibling?.querySelector('p:last-child');

    if (todayCard) todayCard.textContent = stats.today_count || 0;
    if (pendingCard) pendingCard.textContent = stats.scheduled_count || 0;
    if (completedCard) completedCard.textContent = stats.completed_count || 0;
    if (cancelledCard) cancelledCard.textContent = stats.cancelled_count || 0;
}

/**
 * Show loading state
 */
function showLoadingState() {
    const tbody = document.querySelector('.appointments-table tbody');
    if (tbody) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-12">
                    <div class="flex flex-col items-center">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
                        <p class="text-gray-500">Loading appointments...</p>
                    </div>
                </td>
            </tr>
        `;
    }

    // Update results count
    const resultsCounts = document.querySelectorAll('.results-count');
    resultsCounts.forEach(element => {
        element.textContent = 'Loading appointments...';
    });
}

/**
 * Hide loading state
 */
function hideLoadingState() {
    // Loading state is hidden when table is updated
}

/**
 * Show error state
 */
function showErrorState(message) {
    const tbody = document.querySelector('.appointments-table tbody');
    if (tbody) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-12">
                    <div class="flex flex-col items-center">
                        <div class="text-red-500 mb-4">
                            <i class="fas fa-exclamation-triangle text-4xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Error Loading Appointments</h3>
                        <p class="text-gray-500 mb-4">${message}</p>
                        <button onclick="performRealTimeFilter()" class="schedule-appointment-button">
                            <i class="fas fa-refresh mr-2"></i>Try Again
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }
}

// Helper functions for styling
function getStatusClass(status) {
    const statusClasses = {
        'scheduled': 'scheduled',
        'completed': 'completed',
        'cancelled': 'cancelled',
        'no_show': 'no-show',
        'rescheduled': 'rescheduled'
    };
    return statusClasses[status] || 'scheduled';
}

function getPriorityClass(priority) {
    const priorityClasses = {
        'normal': 'normal',
        'high': 'high',
        'urgent': 'urgent'
    };
    return priorityClasses[priority] || 'normal';
}

function getTypeClass(type) {
    const typeClasses = {
        'consultation': 'consultation',
        'follow_up': 'follow-up',
        'check_up': 'check-up',
        'vaccination': 'vaccination',
        'screening': 'screening',
        'emergency': 'emergency'
    };
    return typeClasses[type] || 'consultation';
}

// Appointment management functions
function updateAppointmentStatus(appointmentId, newStatus) {
    if (!confirm(`Are you sure you want to mark this appointment as ${newStatus}?`)) {
        return;
    }

    const formData = new FormData();
    formData.append('status', newStatus);
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

    fetch(`/api/appointments/${appointmentId}/update-status/`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            performRealTimeFilter(); // Refresh the table
        } else {
            showNotification(data.error || 'Failed to update appointment status', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Failed to update appointment status', 'error');
    });
}

function confirmDeleteAppointment(appointmentId, patientName, date, time) {
    if (!confirm(`Are you sure you want to delete the appointment for ${patientName} on ${date} at ${time}?`)) {
        return;
    }

    const formData = new FormData();
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

    fetch(`/api/appointments/${appointmentId}/delete/`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            performRealTimeFilter(); // Refresh the table
        } else {
            showNotification('Failed to delete appointment', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Failed to delete appointment', 'error');
    });
}

function rescheduleAppointment(appointmentId, patientName) {
    // This would open a reschedule modal or redirect to reschedule page
    window.location.href = `/api/appointments/${appointmentId}/reschedule/`;
}

// Simple notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}-notification`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} mr-2"></i>
            ${message}
        </div>
    `;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}
