/**
 * ZimHealth-ID Patient Success Modal - Professional Theme
 * Fixed: No test button, single print dialog, QR inside card
 */

// Global variable to track modal state
let currentModal = null;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initializePatientForm();
});

function initializePatientForm() {
    console.log('Initializing patient form...');
    
    // Find the patient registration form
    const form = document.getElementById('patient-registration-form');
    if (!form) {
        console.log('Patient registration form not found');
        return;
    }
    
    console.log('Patient form found, setting up AJAX submission');
    
    // Override form submission
    form.addEventListener('submit', handlePatientSubmission);
    // Note: Test button removed as requested
}

async function handlePatientSubmission(event) {
    event.preventDefault();
    console.log('Form submitted, processing...');
    
    const form = event.target;
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalBtnText = submitBtn.innerHTML;
    
    // Show loading state
    showLoadingState(submitBtn);
    
    try {
        // Prepare form data
        const formData = new FormData(form);
        
        // Make AJAX request
        const response = await fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': formData.get('csrfmiddlewaretoken')
            }
        });
        
        console.log('Response received:', response.status);
        
        if (!response.ok) {
            throw new Error(`Server error: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('Response data:', data);
        
        if (data.success) {
            // Show success modal
            showSuccessModal(data.patient);
            // Reset form
            form.reset();
            // Show notification
            showNotification('Patient registered successfully!', 'success');
        } else {
            // Show errors
            showFormErrors(data.errors || {});
            showNotification('Please correct the errors and try again.', 'error');
        }
        
    } catch (error) {
        console.error('Submission error:', error);
        showNotification('Failed to register patient. Please try again.', 'error');
    } finally {
        // Reset button
        resetLoadingState(submitBtn, originalBtnText);
    }
}

function showLoadingState(button) {
    button.disabled = true;
    button.innerHTML = `
        <i class="fas fa-spinner fa-spin"></i>
        <span>Registering Patient...</span>
    `;
}

function resetLoadingState(button, originalText) {
    button.disabled = false;
    button.innerHTML = originalText;
}

function showSuccessModal(patient) {
    console.log('Showing success modal for:', patient);
    
    // Remove existing modal
    if (currentModal) {
        currentModal.remove();
    }
    
    // Create modal HTML with QR code inside card
    const modalHTML = createModalHTML(patient);
    
    // Add to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // Get modal element
    currentModal = document.getElementById('patient-success-modal');
    
    // Show modal with animation
    setTimeout(() => {
        currentModal.classList.add('show');
    }, 50);
    
    // Setup event listeners
    setupModalEvents();
}

function createModalHTML(patient) {
    // Format blood type for CSS class
    const bloodTypeClass = patient.blood_type ? 
        patient.blood_type.toLowerCase().replace('+', '-positive').replace('-', '-negative') : 
        'unknown';
    
    // Generate QR code display - this will be inside the card
    let qrCodeDisplay = '';
    if (patient.qr_code_url) {
        qrCodeDisplay = `<img src="${patient.qr_code_url}" alt="Patient QR Code" class="qr-image">`;
    } else {
        qrCodeDisplay = `
            <div class="qr-placeholder">
                <i class="fas fa-qrcode"></i>
                <span>QR Code</span>
                <small>${patient.zimhealth_id}</small>
            </div>`;
    }
    
    return `
        <div id="patient-success-modal" class="patient-success-overlay">
            <div class="patient-success-modal">
                <!-- Modal Header -->
                <div class="modal-header">
                    <button class="close-btn" onclick="closeSuccessModal()">
                        <i class="fas fa-times"></i>
                    </button>
                    <div class="modal-header-content">
                        <div class="success-indicator">
                            <div class="success-checkmark">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="success-text">
                                <h3 class="modal-title">Patient Registration Successful</h3>
                                <p class="modal-subtitle">ZimHealth-ID Generated and QR Code Created</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Modal Body with ID Card -->
                <div class="modal-body">
                    <!-- ID Card with QR Code Inside -->
                    <div class="id-card-container">
                        <div class="id-card">
                            <!-- Card Header -->
                            <div class="card-header">
                                <div class="card-logo">ZimHealth-ID</div>
                                <div class="card-type">Healthcare ID</div>
                            </div>
                            
                            <!-- Card Body -->
                            <div class="card-body">
                                <!-- Patient Info Section -->
                                <div class="patient-info-section">
                                    <div class="patient-name">${patient.full_name}</div>
                                    <div class="patient-id">${patient.zimhealth_id}</div>
                                    
                                    <div class="patient-details">
                                        <div class="detail-row">
                                            <i class="fas fa-calendar"></i>
                                            <span>${formatDate(patient.date_of_birth)}</span>
                                        </div>
                                        <div class="detail-row">
                                            <i class="fas fa-venus-mars"></i>
                                            <span>${patient.gender}</span>
                                        </div>
                                        <div class="detail-row">
                                            <i class="fas fa-tint"></i>
                                            <span>${patient.blood_type || 'Unknown'}</span>
                                        </div>
                                        <div class="detail-row">
                                            <i class="fas fa-phone"></i>
                                            <span>${patient.phone_number}</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- QR Code Section - Inside the card -->
                                <div class="qr-section">
                                    <div class="qr-container">
                                        ${qrCodeDisplay}
                                    </div>
                                    <div class="qr-label">Scan for Info</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="modal-actions">
                        <button onclick="printPatientCard()" class="action-btn btn-primary">
                            <i class="fas fa-print"></i>
                            Print ID Card
                        </button>
                        
                        <a href="/api/patients/${patient.zimhealth_id}/" class="action-btn btn-secondary">
                            <i class="fas fa-user"></i>
                            View Patient
                        </a>
                        
                        <button onclick="registerAnother()" class="action-btn btn-success">
                            <i class="fas fa-user-plus"></i>
                            Register Another
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function setupModalEvents() {
    if (!currentModal) return;
    
    // Close on overlay click
    currentModal.addEventListener('click', function(e) {
        if (e.target === currentModal) {
            closeSuccessModal();
        }
    });
    
    // Close on Escape key
    document.addEventListener('keydown', handleEscapeKey);
}

function handleEscapeKey(e) {
    if (e.key === 'Escape' && currentModal && currentModal.classList.contains('show')) {
        closeSuccessModal();
    }
}

function closeSuccessModal() {
    if (!currentModal) return;
    
    currentModal.classList.remove('show');
    
    setTimeout(() => {
        if (currentModal) {
            currentModal.remove();
            currentModal = null;
        }
        document.removeEventListener('keydown', handleEscapeKey);
    }, 300);
}

function registerAnother() {
    closeSuccessModal();
    
    // Focus on first input
    const firstInput = document.querySelector('input[name="first_name"]');
    if (firstInput) {
        firstInput.focus();
        firstInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
    
    showNotification('Ready for next patient registration', 'info');
}

// Fixed print function - no double dialogs, prints the modal content directly
function printPatientCard() {
    // Create a clean print version of just the ID card
    const idCard = document.querySelector('.id-card-container');
    if (!idCard) {
        showNotification('ID card not found', 'error');
        return;
    }
    
    // Create print window content
    const printContent = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>ZimHealth-ID Card</title>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }
                
                body {
                    font-family: Arial, sans-serif;
                    background: white;
                    padding: 20px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    min-height: 100vh;
                }
                
                .id-card-container {
                    display: flex;
                    justify-content: center;
                }
                
                .id-card {
                    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
                    border-radius: 12px;
                    width: 400px;
                    height: 250px;
                    color: white;
                    position: relative;
                    overflow: hidden;
                    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
                    border: 2px solid #1e3a8a;
                    /* Ensure colors print */
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                    color-adjust: exact;
                }
                
                .card-header {
                    background: rgba(255, 255, 255, 0.95);
                    padding: 10px 15px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    border-bottom: 1px solid rgba(30, 64, 175, 0.2);
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                    color-adjust: exact;
                }
                
                .card-logo {
                    font-size: 18px;
                    font-weight: bold;
                    color: #1e40af;
                }
                
                .card-type {
                    font-size: 12px;
                    color: #64748b;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }
                
                .card-body {
                    padding: 15px;
                    display: flex;
                    justify-content: space-between;
                    height: calc(100% - 50px);
                }
                
                .patient-info-section {
                    flex: 1;
                }
                
                .patient-name {
                    font-size: 20px;
                    font-weight: bold;
                    margin-bottom: 8px;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                }
                
                .patient-id {
                    font-size: 16px;
                    font-family: 'Courier New', monospace;
                    background: rgba(255, 255, 255, 0.2);
                    padding: 6px 10px;
                    border-radius: 6px;
                    display: inline-block;
                    margin-bottom: 15px;
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                    color-adjust: exact;
                }
                
                .patient-details {
                    font-size: 12px;
                    line-height: 1.6;
                }
                
                .detail-row {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-bottom: 4px;
                    opacity: 0.9;
                }
                
                .detail-row i {
                    width: 14px;
                    text-align: center;
                }
                
                .qr-section {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    margin-left: 15px;
                }
                
                .qr-container {
                    background: rgba(255, 255, 255, 0.95);
                    padding: 10px;
                    border-radius: 8px;
                    margin-bottom: 8px;
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                    color-adjust: exact;
                }
                
                .qr-image {
                    width: 80px;
                    height: 80px;
                    object-fit: contain;
                    border-radius: 4px;
                }
                
                .qr-placeholder {
                    width: 80px;
                    height: 80px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    color: #6b7280;
                    font-size: 10px;
                    text-align: center;
                }
                
                .qr-placeholder i {
                    font-size: 24px;
                    margin-bottom: 4px;
                }
                
                .qr-label {
                    font-size: 10px;
                    color: rgba(255, 255, 255, 0.8);
                    text-align: center;
                    font-weight: 500;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }
                
                /* Print specific styles */
                @media print {
                    body {
                        background: white !important;
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                        color-adjust: exact;
                    }
                    
                    .id-card {
                        -webkit-print-color-adjust: exact !important;
                        print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%) !important;
                    }
                    
                    .card-header {
                        -webkit-print-color-adjust: exact !important;
                        print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        background: rgba(255, 255, 255, 0.95) !important;
                    }
                    
                    .patient-id {
                        -webkit-print-color-adjust: exact !important;
                        print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        background: rgba(255, 255, 255, 0.2) !important;
                    }
                    
                    .qr-container {
                        -webkit-print-color-adjust: exact !important;
                        print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        background: rgba(255, 255, 255, 0.95) !important;
                    }
                    
                    @page {
                        margin: 0.5in;
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                        color-adjust: exact;
                    }
                }
            </style>
        </head>
        <body>
            ${idCard.outerHTML}
        </body>
        </html>
    `;
    
    // Create new window and print - this prevents double dialogs
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    printWindow.document.write(printContent);
    printWindow.document.close();
    
    // Wait for content to load, then print
    printWindow.onload = function() {
        setTimeout(() => {
            printWindow.print();
            // Close window after printing
            printWindow.onafterprint = function() {
                printWindow.close();
            };
        }, 500);
    };
    
    showNotification('Opening print dialog...', 'info');
}

function showFormErrors(errors) {
    // Clear existing errors
    document.querySelectorAll('.error-message').forEach(el => el.remove());
    
    // Show new errors
    for (const [fieldName, fieldErrors] of Object.entries(errors)) {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field && fieldErrors.length > 0) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message text-red-600 text-xs mt-1';
            errorDiv.textContent = fieldErrors[0];
            field.parentNode.appendChild(errorDiv);
            
            field.classList.add('border-red-500');
            
            // Remove error on input
            field.addEventListener('input', function() {
                this.classList.remove('border-red-500');
                const errorEl = this.parentNode.querySelector('.error-message');
                if (errorEl) errorEl.remove();
            }, { once: true });
        }
    }
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 16px;
        border-radius: 4px;
        color: white;
        font-weight: 500;
        z-index: 10001;
        max-width: 400px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transform: translateX(100%);
        transition: transform 0.3s ease;
        font-size: 0.875rem;
    `;
    
    const colors = {
        success: '#10b981',
        error: '#ef4444',
        info: '#3b82f6',
        warning: '#f59e0b'
    };
    
    notification.style.background = colors[type] || colors.info;
    
    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" 
                    style="background: none; border: none; color: white; margin-left: 8px; cursor: pointer; font-size: 14px;">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => notification.remove(), 300);
    }, 5000);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Global functions for onclick handlers
window.closeSuccessModal = closeSuccessModal;
window.registerAnother = registerAnother;
window.printPatientCard = printPatientCard;