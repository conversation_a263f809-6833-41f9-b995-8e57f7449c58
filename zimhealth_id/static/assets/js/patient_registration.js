/**
 * Patient Registration Success System
 * Handles AJAX form submission and success modal display
 */

document.addEventListener('DOMContentLoaded', function() {
    initializePatientRegistration();
});

function initializePatientRegistration() {
    // Use the specific form ID for patient registration
    const form = document.getElementById('patient-registration-form') || document.querySelector('form[method="post"]');
    if (!form) {
        console.error('Patient registration form not found');
        return;
    }

    console.log('Patient registration form found:', form);

    // Add AJAX submission handler
    form.addEventListener('submit', handleFormSubmission);
    
    // Initialize modal close handlers
    initializeModalHandlers();
    
    // Add test button for debugging (temporary)
    addTestButton();
}

function addTestButton() {
    // Create a test button to verify modal works
    const testButton = document.createElement('button');
    testButton.textContent = 'Test Success Modal';
    testButton.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 10000;
        background: #10b981;
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 12px;
    `;
    testButton.onclick = function() {
        console.log('Testing success modal...');
        const testPatient = {
            full_name: 'Test Patient',
            zimhealth_id: 'ZH-***********',
            initials: 'TP',
            date_of_birth: '1990-01-01',
            gender: 'Male',
            blood_type: 'A+',
            phone_number: '+263123456789',
            emergency_contact: '+263987654321',
            qr_code_url: null,
            registration_date: 'January 01, 2024 at 12:00 PM'
        };
        showSuccessModal(testPatient);
    };
    document.body.appendChild(testButton);
}

async function handleFormSubmission(event) {
    event.preventDefault();
    console.log('Form submission intercepted');
    
    const form = event.target;
    const submitButton = form.querySelector('button[type="submit"]');
    const originalButtonText = submitButton.innerHTML;
    
    console.log('Form action:', form.action);
    console.log('Submit button found:', submitButton);
    
    // Show loading state
    setLoadingState(submitButton, true);
    
    try {
        const formData = new FormData(form);
        console.log('FormData created, CSRF token:', formData.get('csrfmiddlewaretoken'));
        
        const response = await fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': formData.get('csrfmiddlewaretoken')
            }
        });
        
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            console.error('Response is not JSON:', contentType);
            const text = await response.text();
            console.error('Response text:', text);
            throw new Error('Server did not return JSON response');
        }
        
        const data = await response.json();
        console.log('Response data:', data);
        
        if (data.success) {
            console.log('Success! Showing modal with patient data:', data.patient);
            // Show success modal
            showSuccessModal(data.patient);
            
            // Reset form for next entry
            form.reset();
            
            // Show success notification
            showNotification(data.message, 'success');
            
        } else {
            console.log('Form validation errors:', data.errors);
            // Handle form errors
            displayFormErrors(data.errors);
            showNotification(data.message || 'Please correct the errors and try again.', 'error');
        }
        
    } catch (error) {
        console.error('Form submission error:', error);
        showNotification('An error occurred while registering the patient. Please try again.', 'error');
    } finally {
        // Reset button state
        setLoadingState(submitButton, false, originalButtonText);
    }
}

function setLoadingState(button, isLoading, originalText = '') {
    if (isLoading) {
        button.disabled = true;
        button.innerHTML = `
            <i class="fas fa-spinner fa-spin"></i>
            <span>Registering Patient...</span>
        `;
    } else {
        button.disabled = false;
        button.innerHTML = originalText || `
            <i class="fas fa-save"></i>
            <span>Save Patient</span>
        `;
    }
}

function showSuccessModal(patient) {
    // Create modal HTML
    const modalHTML = createSuccessModalHTML(patient);
    
    // Remove existing modal if any
    const existingModal = document.getElementById('patient-success-modal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // Show modal with animation
    setTimeout(() => {
        const modal = document.getElementById('patient-success-modal');
        modal.classList.add('active');
    }, 100);
    
    // Initialize modal handlers
    initializeModalHandlers();
}

function createSuccessModalHTML(patient) {
    return `
        <div id="patient-success-modal" class="success-modal-overlay">
            <div class="success-modal">
                <!-- Modal Header -->
                <div class="success-modal-header">
                    <button class="close-modal-btn" onclick="closeSuccessModal()">
                        <i class="fas fa-times"></i>
                    </button>
                    <div class="success-icon">
                        <i class="fas fa-check"></i>
                    </div>
                    <h2 class="success-title">Patient Registered Successfully!</h2>
                    <p class="success-subtitle">ZimHealth-ID has been generated and QR code created</p>
                </div>

                <!-- Modal Body -->
                <div class="success-modal-body">
                    <div class="patient-summary">
                        <div class="patient-info">
                            <h3 class="patient-name">${patient.full_name}</h3>
                            <div class="patient-id">${patient.zimhealth_id}</div>
                            
                            <div class="patient-details">
                                <div class="detail-item">
                                    <i class="fas fa-calendar detail-icon"></i>
                                    <span class="detail-label">DOB:</span>
                                    <span class="detail-value">${formatDate(patient.date_of_birth)}</span>
                                </div>
                                <div class="detail-item">
                                    <i class="fas fa-venus-mars detail-icon"></i>
                                    <span class="detail-label">Gender:</span>
                                    <span class="detail-value">${patient.gender}</span>
                                </div>
                                <div class="detail-item">
                                    <i class="fas fa-tint detail-icon"></i>
                                    <span class="detail-label">Blood:</span>
                                    <span class="detail-value">${patient.blood_type}</span>
                                </div>
                                <div class="detail-item">
                                    <i class="fas fa-phone detail-icon"></i>
                                    <span class="detail-label">Phone:</span>
                                    <span class="detail-value">${patient.phone_number}</span>
                                </div>
                                <div class="detail-item">
                                    <i class="fas fa-exclamation-triangle detail-icon"></i>
                                    <span class="detail-label">Emergency:</span>
                                    <span class="detail-value">${patient.emergency_contact}</span>
                                </div>
                                <div class="detail-item">
                                    <i class="fas fa-clock detail-icon"></i>
                                    <span class="detail-label">Registered:</span>
                                    <span class="detail-value">${patient.registration_date}</span>
                                </div>
                            </div>
                        </div>

                        <div class="qr-initials-section">
                            <div class="patient-initials">${patient.initials}</div>
                            
                            <div class="qr-code-container">
                                ${patient.qr_code_url ? 
                                    `<img src="${patient.qr_code_url}" alt="Patient QR Code" class="qr-code-image">` :
                                    `<div class="qr-code-image" style="display: flex; align-items: center; justify-content: center; background: #f3f4f6; color: #6b7280;">QR Code</div>`
                                }
                                <div class="qr-label">Scan for instant access</div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="modal-actions">
                        <a href="/api/patients/${patient.zimhealth_id}/id-card/" 
                           class="modal-btn modal-btn-primary" 
                           target="_blank">
                            <i class="fas fa-print"></i>
                            Print ID Card
                        </a>
                        <a href="/api/patients/${patient.zimhealth_id}/" 
                           class="modal-btn modal-btn-secondary">
                            <i class="fas fa-user"></i>
                            View Patient
                        </a>
                        <button onclick="registerAnotherPatient()" 
                                class="modal-btn modal-btn-success">
                            <i class="fas fa-user-plus"></i>
                            Register Another
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function initializeModalHandlers() {
    // Close modal when clicking overlay
    const modal = document.getElementById('patient-success-modal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeSuccessModal();
            }
        });
    }
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal && modal.classList.contains('active')) {
            closeSuccessModal();
        }
    });
}

function closeSuccessModal() {
    const modal = document.getElementById('patient-success-modal');
    if (modal) {
        modal.classList.remove('active');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

function registerAnotherPatient() {
    closeSuccessModal();
    // Form is already reset, just focus on first input
    const firstInput = document.querySelector('input[name="first_name"]');
    if (firstInput) {
        firstInput.focus();
    }
}

function displayFormErrors(errors) {
    // Clear existing errors
    document.querySelectorAll('.text-red-600').forEach(el => el.remove());
    
    // Display new errors
    for (const [fieldName, fieldErrors] of Object.entries(errors)) {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field && fieldErrors.length > 0) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'text-red-600 text-xs mt-1';
            errorDiv.textContent = fieldErrors[0];
            field.parentNode.appendChild(errorDiv);
            
            // Add error styling to field
            field.classList.add('border-red-500');
            
            // Remove error styling on input
            field.addEventListener('input', function() {
                this.classList.remove('border-red-500');
                const errorEl = this.parentNode.querySelector('.text-red-600');
                if (errorEl) errorEl.remove();
            }, { once: true });
        }
    }
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function showNotification(message, type = 'info') {
    // Use existing notification system if available
    if (typeof window.showNotification === 'function') {
        window.showNotification(message, type);
        return;
    }
    
    // Fallback notification
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 16px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    // Set background color based on type
    const colors = {
        success: '#10b981',
        error: '#ef4444',
        info: '#3b82f6',
        warning: '#f59e0b'
    };
    notification.style.background = colors[type] || colors.info;
    
    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: white; margin-left: 8px; cursor: pointer;">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => notification.remove(), 300);
    }, 5000);
}
