/* Professional Prescription Detail JavaScript for ZimHealth-ID */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize prescription detail functionality
    initializePrescriptionDetail();
});

function initializePrescriptionDetail() {
    // Add any additional initialization here
    console.log('Prescription detail page initialized');
    
    // Ensure proper layout on load
    adjustLayoutForSinglePage();
    
    // Handle window resize
    window.addEventListener('resize', adjustLayoutForSinglePage);
}

function adjustLayoutForSinglePage() {
    // Ensure the layout fits within viewport
    const container = document.querySelector('.prescription-detail-container');
    if (container) {
        const viewportHeight = window.innerHeight;
        const headerHeight = document.querySelector('.detail-header')?.offsetHeight || 0;
        const availableHeight = viewportHeight - headerHeight;
        
        const contentArea = container.querySelector('.max-w-7xl');
        if (contentArea) {
            contentArea.style.maxHeight = `${availableHeight}px`;
        }
    }
}

// Enhanced status update function with better UX
function updatePrescriptionStatus(status) {
    const statusLabels = {
        'completed': 'completed',
        'on_hold': 'on hold',
        'discontinued': 'discontinued'
    };
    
    const statusLabel = statusLabels[status] || status;
    
    if (confirm(`Are you sure you want to mark this prescription as ${statusLabel}?`)) {
        // Show loading state
        const buttons = document.querySelectorAll('.status-actions .btn');
        buttons.forEach(btn => {
            btn.disabled = true;
            btn.style.opacity = '0.6';
        });
        
        const formData = new FormData();
        formData.append('status', status);
        
        // Get CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
                         document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        
        if (csrfToken) {
            formData.append('csrfmiddlewaretoken', csrfToken);
        }
        
        // Get the prescription ID from the URL or data attribute
        const prescriptionId = getPrescriptionId();
        
        if (!prescriptionId) {
            console.error('Prescription ID not found');
            alert('Error: Could not identify prescription');
            return;
        }
        
        fetch(`/api/prescriptions/${prescriptionId}/update-status-ajax/`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': csrfToken,
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                showNotification(`Prescription status updated to ${statusLabel}`, 'success');
                
                // Reload page after short delay
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                console.error('Server error:', data.error);
                alert('Error updating status: ' + (data.error || 'Unknown error'));
                
                // Re-enable buttons
                buttons.forEach(btn => {
                    btn.disabled = false;
                    btn.style.opacity = '1';
                });
            }
        })
        .catch(error => {
            console.error('Network error:', error);
            alert('An error occurred while updating the status. Please try again.');
            
            // Re-enable buttons
            buttons.forEach(btn => {
                btn.disabled = false;
                btn.style.opacity = '1';
            });
        });
    }
}

function getPrescriptionId() {
    // Try to get from URL
    const urlParts = window.location.pathname.split('/');
    const prescriptionIndex = urlParts.indexOf('prescriptions');
    if (prescriptionIndex !== -1 && urlParts[prescriptionIndex + 1]) {
        return urlParts[prescriptionIndex + 1];
    }
    
    // Try to get from data attribute
    const container = document.querySelector('.prescription-detail-container');
    if (container && container.dataset.prescriptionId) {
        return container.dataset.prescriptionId;
    }
    
    // Try to get from metadata
    const metadataId = document.querySelector('.metadata-item p')?.textContent;
    if (metadataId && metadataId.startsWith('#')) {
        return metadataId.substring(1);
    }
    
    return null;
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#22c55e' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        font-size: 14px;
        font-weight: 500;
        max-width: 300px;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after delay
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Enhanced print function
function printPrescription() {
    // Hide action buttons and sidebar for printing
    const elementsToHide = document.querySelectorAll('.status-actions, .action-list, .btn');
    const originalDisplay = [];
    
    elementsToHide.forEach((element, index) => {
        originalDisplay[index] = element.style.display;
        element.style.display = 'none';
    });
    
    // Add print-specific styles
    const printStyles = document.createElement('style');
    printStyles.textContent = `
        @media print {
            .prescription-detail-container {
                background: white !important;
                max-height: none !important;
                overflow: visible !important;
            }
            .prescription-detail-container::before {
                display: none !important;
            }
            .detail-header {
                background: white !important;
                border-bottom: 2px solid #000 !important;
                box-shadow: none !important;
            }
            .detail-card {
                background: white !important;
                border: 1px solid #000 !important;
                box-shadow: none !important;
                break-inside: avoid;
            }
            .card-header {
                background: #f5f5f5 !important;
                border-bottom: 1px solid #000 !important;
            }
        }
    `;
    document.head.appendChild(printStyles);
    
    // Print
    window.print();
    
    // Restore elements after printing
    setTimeout(() => {
        elementsToHide.forEach((element, index) => {
            element.style.display = originalDisplay[index];
        });
        document.head.removeChild(printStyles);
    }, 1000);
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl+P for print
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        printPrescription();
    }
    
    // Escape to go back
    if (e.key === 'Escape') {
        const backButton = document.querySelector('a[href*="prescriptions"]');
        if (backButton) {
            window.location.href = backButton.href;
        }
    }
});

// Smooth scrolling for internal links
document.addEventListener('click', function(e) {
    if (e.target.matches('a[href^="#"]')) {
        e.preventDefault();
        const target = document.querySelector(e.target.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }
});

// Auto-save scroll position
let scrollTimeout;
window.addEventListener('scroll', function() {
    clearTimeout(scrollTimeout);
    scrollTimeout = setTimeout(() => {
        sessionStorage.setItem('prescriptionDetailScrollPos', window.pageYOffset);
    }, 100);
});

// Restore scroll position
window.addEventListener('load', function() {
    const scrollPos = sessionStorage.getItem('prescriptionDetailScrollPos');
    if (scrollPos) {
        window.scrollTo(0, parseInt(scrollPos));
        sessionStorage.removeItem('prescriptionDetailScrollPos');
    }
});

// Export functions for global access
window.updatePrescriptionStatus = updatePrescriptionStatus;
window.printPrescription = printPrescription;