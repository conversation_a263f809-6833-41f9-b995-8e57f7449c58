from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from api.models import Patient
from patient_portal.models import PatientUser


class Command(BaseCommand):
    help = "Check for user account conflicts that might prevent patient registration"

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("Checking for user account conflicts..."))

        # Check for Users with ZimHealth-ID format usernames
        zimhealth_users = User.objects.filter(username__startswith="ZH-")
        self.stdout.write(
            f"Found {zimhealth_users.count()} users with ZimHealth-ID format usernames:"
        )

        for user in zimhealth_users:
            self.stdout.write(f"  - Username: {user.username}")

            # Check if this user has a patient_portal_profile
            try:
                patient_user = user.patient_portal_profile
                self.stdout.write(
                    f"    ✓ Has PatientUser: {patient_user.patient.zimhealth_id}"
                )
            except PatientUser.DoesNotExist:
                self.stdout.write(f"    ✗ No PatientUser found")

            # Check if this user has a patient_profile
            try:
                patient = user.patient_profile
                self.stdout.write(f"    ✓ Has Patient profile: {patient.zimhealth_id}")
            except Patient.DoesNotExist:
                self.stdout.write(f"    ✗ No Patient profile found")

        # Check for Patients with user relationships
        patients_with_users = Patient.objects.filter(user__isnull=False)
        self.stdout.write(
            f"\nFound {patients_with_users.count()} patients with direct user relationships:"
        )

        for patient in patients_with_users:
            self.stdout.write(
                f"  - Patient: {patient.zimhealth_id} -> User: {patient.user.username}"
            )

        # Check for PatientUser objects
        patient_users = PatientUser.objects.all()
        self.stdout.write(f"\nFound {patient_users.count()} PatientUser objects:")

        for pu in patient_users:
            self.stdout.write(
                f"  - PatientUser: {pu.patient.zimhealth_id} -> User: {pu.user.username}"
            )

        # Check for orphaned relationships
        self.stdout.write(f"\nChecking for orphaned relationships...")

        # Users with ZimHealth-ID usernames but no patient relationships
        orphaned_users = []
        for user in zimhealth_users:
            has_patient_user = hasattr(user, "patient_portal_profile")
            has_patient_profile = hasattr(user, "patient_profile")

            if not has_patient_user and not has_patient_profile:
                orphaned_users.append(user)

        if orphaned_users:
            self.stdout.write(
                self.style.WARNING(f"Found {len(orphaned_users)} orphaned users:")
            )
            for user in orphaned_users:
                self.stdout.write(f"  - {user.username} (ID: {user.id})")
        else:
            self.stdout.write(self.style.SUCCESS("No orphaned users found."))

        self.stdout.write(self.style.SUCCESS("\nConflict check completed."))
