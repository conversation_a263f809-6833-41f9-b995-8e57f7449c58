from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from api.models import Patient
from patient_portal.models import PatientUser


class Command(BaseCommand):
    help = "Clean up orphaned user accounts that have no patient relationships"

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be deleted without actually deleting",
        )
        parser.add_argument(
            "--force",
            action="store_true",
            help="Actually delete orphaned users (use with caution)",
        )

    def handle(self, *args, **options):
        dry_run = options["dry_run"]
        force = options["force"]

        if not dry_run and not force:
            self.stdout.write(
                self.style.WARNING(
                    "This command will delete orphaned users. Use --dry-run to see what would be deleted, "
                    "or --force to actually delete them."
                )
            )
            return

        self.stdout.write(self.style.SUCCESS("Finding orphaned users..."))

        # Find users with ZimHealth-ID format usernames
        zimhealth_users = User.objects.filter(username__startswith="ZH-")
        orphaned_users = []

        for user in zimhealth_users:
            has_patient_user = hasattr(user, "patient_portal_profile")
            has_patient_profile = hasattr(user, "patient_profile")

            if not has_patient_user and not has_patient_profile:
                orphaned_users.append(user)

        if not orphaned_users:
            self.stdout.write(self.style.SUCCESS("No orphaned users found."))
            return

        self.stdout.write(f"Found {len(orphaned_users)} orphaned users:")

        for user in orphaned_users:
            self.stdout.write(
                f"  - {user.username} (ID: {user.id}, Email: {user.email})"
            )

            if dry_run:
                self.stdout.write(f"    [DRY RUN] Would delete this user")
            elif force:
                try:
                    user.delete()
                    self.stdout.write(
                        self.style.SUCCESS(f"    ✓ Deleted user {user.username}")
                    )
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(
                            f"    ✗ Failed to delete user {user.username}: {e}"
                        )
                    )

        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    f"\n[DRY RUN] Would delete {len(orphaned_users)} orphaned users. "
                    "Use --force to actually delete them."
                )
            )
        elif force:
            self.stdout.write(
                self.style.SUCCESS(
                    f"\nCleanup completed. Processed {len(orphaned_users)} orphaned users."
                )
            )
