from django.contrib import admin
from .models import Patient<PERSON>ser, PatientLoginAttempt


@admin.register(PatientUser)
class PatientUserAdmin(admin.ModelAdmin):
    """Admin interface for patient portal users"""

    list_display = (
        "get_zimhealth_id",
        "get_patient_name",
        "is_verified",
        "registration_date",
        "last_login_date",
        "failed_login_attempts",
        "is_account_locked",
    )
    list_filter = (
        "is_verified",
        "registration_date",
        "terms_accepted",
        "privacy_policy_accepted",
    )
    search_fields = (
        "patient__zimhealth_id",
        "patient__first_name",
        "patient__last_name",
        "user__username",
    )
    readonly_fields = (
        "registration_date",
        "last_login_date",
        "get_zimhealth_id",
        "get_patient_name",
    )
    ordering = ("-registration_date",)

    def get_zimhealth_id(self, obj):
        return obj.patient.zimhealth_id

    get_zimhealth_id.short_description = "ZimHealth-ID"

    def get_patient_name(self, obj):
        return obj.patient.full_name

    get_patient_name.short_description = "Patient Name"

    def is_account_locked(self, obj):
        return obj.is_account_locked()

    is_account_locked.boolean = True
    is_account_locked.short_description = "Account Locked"

    actions = ["unlock_accounts", "verify_patients"]

    def unlock_accounts(self, request, queryset):
        """Admin action to unlock patient accounts"""
        count = 0
        for patient_user in queryset:
            if patient_user.is_account_locked():
                patient_user.unlock_account()
                count += 1

        self.message_user(request, f"Unlocked {count} patient accounts.")

    unlock_accounts.short_description = "Unlock selected patient accounts"

    def verify_patients(self, request, queryset):
        """Admin action to verify patient accounts"""
        count = queryset.filter(is_verified=False).update(is_verified=True)
        self.message_user(request, f"Verified {count} patient accounts.")

    verify_patients.short_description = "Verify selected patient accounts"


@admin.register(PatientLoginAttempt)
class PatientLoginAttemptAdmin(admin.ModelAdmin):
    """Admin interface for patient login attempts"""

    list_display = (
        "zimhealth_id_attempted",
        "success",
        "timestamp",
        "ip_address",
        "failure_reason",
    )
    list_filter = ("success", "timestamp", "failure_reason")
    search_fields = ("zimhealth_id_attempted", "ip_address")
    readonly_fields = (
        "patient_user",
        "zimhealth_id_attempted",
        "ip_address",
        "user_agent",
        "success",
        "timestamp",
        "failure_reason",
    )
    ordering = ("-timestamp",)

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False
