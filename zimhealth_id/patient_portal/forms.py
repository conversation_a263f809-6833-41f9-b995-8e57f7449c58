from django import forms
from django.contrib.auth.forms import AuthenticationForm, UserCreationForm
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils import timezone
from api.models import Patient
from .models import PatientUser
import re


class PatientRegistrationForm(UserCreationForm):
    """Patient registration form using ZimHealth-ID"""

    zimhealth_id = forms.CharField(
        max_length=20,
        widget=forms.TextInput(
            attrs={
                "class": "auth-input",
                "placeholder": "ZH-YYYY-XXXXXX",
                "pattern": "ZH-[0-9]{4}-[0-9]{6}",
                "title": "Enter your ZimHealth-ID in format ZH-YYYY-XXXXXX",
            }
        ),
        help_text="Enter your ZimHealth-ID (found on your patient card or QR code)",
    )

    first_name = forms.Char<PERSON><PERSON>(
        max_length=30,
        widget=forms.TextInput(
            attrs={"class": "auth-input", "placeholder": "First Name"}
        ),
    )

    last_name = forms.CharField(
        max_length=30,
        widget=forms.TextInput(
            attrs={"class": "auth-input", "placeholder": "Last Name"}
        ),
    )

    email = forms.EmailField(
        widget=forms.EmailInput(
            attrs={"class": "auth-input", "placeholder": "Email Address"}
        ),
        help_text="We'll use this to send you important health updates",
    )

    phone_number = forms.CharField(
        max_length=15,
        widget=forms.TextInput(
            attrs={"class": "auth-input", "placeholder": "+263XXXXXXXXX"}
        ),
        help_text="Your phone number for appointment reminders",
    )

    terms_accepted = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(attrs={"class": "auth-checkbox"}),
        label="I accept the Terms of Service and Privacy Policy",
    )

    class Meta:
        model = User
        fields = (
            "zimhealth_id",
            "first_name",
            "last_name",
            "email",
            "phone_number",
            "password1",
            "password2",
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Customize password fields
        self.fields["password1"].widget.attrs.update(
            {"class": "auth-input", "placeholder": "Create Password"}
        )
        self.fields["password2"].widget.attrs.update(
            {"class": "auth-input", "placeholder": "Confirm Password"}
        )

        # Remove username field as we'll use ZimHealth-ID
        if "username" in self.fields:
            del self.fields["username"]

    def clean_zimhealth_id(self):
        """Validate ZimHealth-ID format and existence"""
        zimhealth_id = self.cleaned_data.get("zimhealth_id", "").strip().upper()

        # Validate format
        if not re.match(r"^ZH-\d{4}-\d{6}$", zimhealth_id):
            raise ValidationError(
                "Invalid ZimHealth-ID format. Please use format: ZH-YYYY-XXXXXX"
            )

        # Check if patient exists in the main database
        try:
            patient = Patient.objects.get(zimhealth_id=zimhealth_id, is_active=True)
        except Patient.DoesNotExist:
            raise ValidationError(
                "ZimHealth-ID not found. Please contact your healthcare provider to register as a patient first."
            )

        # Check if patient already has a portal account (PatientUser)
        if PatientUser.objects.filter(patient=patient).exists():
            raise ValidationError(
                "A portal account already exists for this ZimHealth-ID. Please use the login form instead."
            )

        # Check if a User with this ZimHealth-ID already exists
        if User.objects.filter(username=zimhealth_id).exists():
            raise ValidationError(
                "An account with this ZimHealth-ID already exists. Please use the login form instead."
            )

        # Note: We allow patients to register even if Patient.user is set
        # This supports the workflow: Provider creates Patient → Patient creates portal account

        return zimhealth_id

    def clean_email(self):
        """Validate email uniqueness"""
        email = self.cleaned_data.get("email")
        if User.objects.filter(email=email).exists():
            raise ValidationError("An account with this email already exists.")
        return email

    def clean(self):
        """Additional validation to match patient data"""
        cleaned_data = super().clean()
        zimhealth_id = cleaned_data.get("zimhealth_id")
        first_name = cleaned_data.get("first_name")
        last_name = cleaned_data.get("last_name")

        if zimhealth_id and first_name and last_name:
            try:
                patient = Patient.objects.get(zimhealth_id=zimhealth_id, is_active=True)

                # Verify name matches (case-insensitive)
                if (
                    patient.first_name.lower() != first_name.lower()
                    or patient.last_name.lower() != last_name.lower()
                ):
                    raise ValidationError(
                        "The name you entered doesn't match our records for this ZimHealth-ID. "
                        "Please ensure you're using the correct information."
                    )

            except Patient.DoesNotExist:
                pass  # Already handled in clean_zimhealth_id

        return cleaned_data

    def save(self, commit=True):
        """Create User and PatientUser objects"""
        # Create User with ZimHealth-ID as username
        zimhealth_id = self.cleaned_data["zimhealth_id"]
        user = super().save(commit=False)
        user.username = zimhealth_id
        user.first_name = self.cleaned_data["first_name"]
        user.last_name = self.cleaned_data["last_name"]
        user.email = self.cleaned_data["email"]

        if commit:
            from django.db import transaction, IntegrityError

            # Double-check for existing user right before saving
            if User.objects.filter(username=zimhealth_id).exists():
                raise ValidationError(
                    "An account with this ZimHealth-ID already exists. Please use the login form instead."
                )

            try:
                with transaction.atomic():
                    user.save()

                    # Get the patient record
                    patient = Patient.objects.get(zimhealth_id=zimhealth_id)

                    # Set the direct relationship from Patient to User (only if not already set)
                    # This supports both workflows:
                    # 1. Provider creates Patient → Patient creates portal account
                    # 2. Patient creates portal account directly
                    if patient.user is None:
                        patient.user = user
                        patient.save()

                    # Create PatientUser (the main relationship for patient portal)
                    patient_user = PatientUser.objects.create(
                        user=user,
                        patient=patient,
                        terms_accepted=self.cleaned_data["terms_accepted"],
                        privacy_policy_accepted=self.cleaned_data["terms_accepted"],
                        terms_accepted_date=timezone.now(),
                    )

            except IntegrityError as e:
                # Handle specific database integrity errors
                error_msg = str(e).lower()
                if "username" in error_msg:
                    raise ValidationError(
                        "An account with this ZimHealth-ID already exists. Please use the login form instead."
                    )
                elif "email" in error_msg:
                    raise ValidationError(
                        "An account with this email address already exists. Please use a different email."
                    )
                else:
                    raise ValidationError(
                        "Registration failed due to a data conflict. Please try again or contact support."
                    )
            except Patient.DoesNotExist:
                raise ValidationError(
                    "Patient record not found. Please contact your healthcare provider."
                )
            except Exception as e:
                # Handle any other unexpected errors
                raise ValidationError(
                    f"Registration failed: {str(e)}. Please try again or contact support."
                )

        return user


class PatientLoginForm(AuthenticationForm):
    """Custom login form for patients using ZimHealth-ID"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Customize username field for ZimHealth-ID
        self.fields["username"].label = "ZimHealth-ID"
        self.fields["username"].widget.attrs.update(
            {
                "class": "auth-input",
                "placeholder": "ZH-YYYY-XXXXXX",
                "pattern": "ZH-[0-9]{4}-[0-9]{6}",
                "title": "Enter your ZimHealth-ID in format ZH-YYYY-XXXXXX",
            }
        )
        self.fields["username"].help_text = (
            "Enter your ZimHealth-ID (found on your patient card)"
        )

        # Customize password field
        self.fields["password"].widget.attrs.update(
            {"class": "auth-input", "placeholder": "Password"}
        )

    def clean_username(self):
        """Validate and format ZimHealth-ID"""
        username = self.cleaned_data.get("username", "").strip().upper()

        # Validate format
        if not re.match(r"^ZH-\d{4}-\d{6}$", username):
            raise ValidationError(
                "Invalid ZimHealth-ID format. Please use format: ZH-YYYY-XXXXXX"
            )

        return username

    def confirm_login_allowed(self, user):
        """Additional checks for patient portal login"""
        super().confirm_login_allowed(user)

        # Check if user has a patient profile
        try:
            patient_user = user.patient_portal_profile

            # Check if account is locked
            if patient_user.is_account_locked():
                raise ValidationError(
                    "Your account is temporarily locked due to multiple failed login attempts. "
                    "Please try again later or contact support."
                )

        except PatientUser.DoesNotExist:
            raise ValidationError(
                "This account is not registered for the patient portal."
            )


class PatientProfileUpdateForm(forms.ModelForm):
    """Form for patients to update their contact information"""

    email = forms.EmailField(
        widget=forms.EmailInput(
            attrs={"class": "government-input", "placeholder": "Email Address"}
        )
    )

    phone_number = forms.CharField(
        max_length=15,
        widget=forms.TextInput(
            attrs={"class": "government-input", "placeholder": "+263XXXXXXXXX"}
        ),
    )

    class Meta:
        model = User
        fields = ("email",)

    def __init__(self, *args, **kwargs):
        self.patient_user = kwargs.pop("patient_user", None)
        super().__init__(*args, **kwargs)

        if self.patient_user:
            # Pre-populate phone number from patient record
            self.fields["phone_number"].initial = self.patient_user.patient.phone_number

    def save(self, commit=True):
        """Update both User and Patient records"""
        user = super().save(commit=commit)

        if commit and self.patient_user:
            # Update patient phone number
            phone_number = self.cleaned_data.get("phone_number")
            if phone_number:
                self.patient_user.patient.phone_number = phone_number
                self.patient_user.patient.save()

        return user
