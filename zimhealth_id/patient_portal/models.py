from django.db import models
from django.contrib.auth.models import User
from django.core.validators import RegexValidator
from django.utils import timezone
from api.models import Patient


class PatientUser(models.Model):
    """
    Patient portal user model that links Django User to Patient records.
    Patients authenticate using their ZimHealth-ID as username.
    """

    user = models.OneToOneField(
        User, on_delete=models.CASCADE, related_name="patient_portal_profile"
    )
    patient = models.OneToOneField(
        Patient, on_delete=models.CASCADE, related_name="portal_user"
    )

    # Registration and verification
    is_verified = models.BooleanField(
        default=False, help_text="Whether the patient has verified their identity"
    )
    registration_date = models.DateTimeField(auto_now_add=True)
    last_login_date = models.DateTimeField(null=True, blank=True)

    # Security tracking
    failed_login_attempts = models.IntegerField(default=0)
    account_locked_until = models.DateTimeField(null=True, blank=True)

    # Terms and privacy
    terms_accepted = models.<PERSON><PERSON>anField(default=False)
    privacy_policy_accepted = models.<PERSON><PERSON>anField(default=False)
    terms_accepted_date = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Patient Portal User: {self.patient.zimhealth_id} - {self.patient.full_name}"

    def is_account_locked(self):
        """Check if account is currently locked"""
        if self.account_locked_until:
            return timezone.now() < self.account_locked_until
        return False

    def unlock_account(self):
        """Unlock the account and reset failed attempts"""
        self.account_locked_until = None
        self.failed_login_attempts = 0
        self.save()

    def record_failed_login(self):
        """Record a failed login attempt and lock account if necessary"""
        self.failed_login_attempts += 1

        # Lock account after 5 failed attempts for 30 minutes
        if self.failed_login_attempts >= 5:
            self.account_locked_until = timezone.now() + timezone.timedelta(minutes=30)

        self.save()

    def record_successful_login(self):
        """Record successful login and reset failed attempts"""
        self.failed_login_attempts = 0
        self.account_locked_until = None
        self.last_login_date = timezone.now()
        self.save()

    class Meta:
        verbose_name = "Patient Portal User"
        verbose_name_plural = "Patient Portal Users"
        ordering = ["-registration_date"]


class PatientLoginAttempt(models.Model):
    """Track patient portal login attempts for security"""

    patient_user = models.ForeignKey(
        PatientUser,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="login_attempts",
    )
    zimhealth_id_attempted = models.CharField(
        max_length=20, help_text="ZimHealth-ID that was attempted for login"
    )
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True, null=True)
    success = models.BooleanField(default=False)
    timestamp = models.DateTimeField(auto_now_add=True)
    failure_reason = models.CharField(
        max_length=100, blank=True, help_text="Reason for login failure"
    )

    def __str__(self):
        status = "Success" if self.success else "Failed"
        return (
            f"{status} patient login: {self.zimhealth_id_attempted} at {self.timestamp}"
        )

    class Meta:
        verbose_name = "Patient Login Attempt"
        verbose_name_plural = "Patient Login Attempts"
        ordering = ["-timestamp"]
