{% extends 'patient_portal/base.html' %}
{% load static %}

{% block title %}My Health Dashboard - ZimHealth-ID{% endblock %}

{% block content %}
<div class="page-container">
    <!-- Professional Dashboard Header -->
    <div class="page-header">
        <div class="flex justify-between items-start">
            <!-- Left Section: Clean Professional Title -->
            <div>
                <h1 class="page-title">Patient Dashboard</h1>
                <p class="page-subtitle">Welcome, {{ patient.first_name }}!</p>
            </div>

            <!-- Right Section: User Information -->
            <div class="flex items-center space-x-4">
                <div class="text-right">
                    <p class="font-medium text-gray-900">{{ patient.zimhealth_id }}</p>
                    <p class="text-sm text-gray-500">{{ patient_user.last_login_date|date:"M d, Y H:i"|default:"First time login" }}</p>
                </div>
                <div class="w-12 h-12 bg-medical-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-medical-600"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Professional Dashboard Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">

        <!-- Enhanced Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Medical Records -->
            <div class="stat-card medical-records p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-icon medical">
                            <i class="fas fa-file-medical text-medical-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="stat-label">Medical Records</p>
                        <p class="stat-value">{{ total_records }}</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-health-600 font-medium">
                        <i class="fas fa-eye mr-1"></i>
                        <span>View-only access</span>
                    </div>
                </div>
            </div>

            <!-- Prescriptions -->
            <div class="stat-card prescriptions p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-icon health">
                            <i class="fas fa-pills text-health-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="stat-label">Prescriptions</p>
                        <p class="stat-value">{{ total_prescriptions }}</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-medical-600 font-medium">
                        <i class="fas fa-pills mr-1"></i>
                        <span>Active medications</span>
                    </div>
                </div>
            </div>

            <!-- Appointments -->
            <div class="stat-card appointments p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-icon medical">
                            <i class="fas fa-calendar-check text-medical-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="stat-label">Appointments</p>
                        <p class="stat-value">{{ total_appointments }}</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-health-600 font-medium">
                        <i class="fas fa-calendar mr-1"></i>
                        <span>Scheduled visits</span>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="stat-card activity p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-icon health">
                            <i class="fas fa-chart-line text-health-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="stat-label">Recent Activity</p>
                        <p class="stat-value">{{ recent_activity_count }}</p>
                        <p class="stat-sublabel">Last 30 days</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-medical-600 font-medium">
                        <i class="fas fa-clock mr-1"></i>
                        <span>Recent updates</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Recent Medical Records -->
            <div class="content-card">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-file-medical text-medical-600 mr-2"></i>
                        Recent Medical Records
                    </h2>
                    <a href="{% url 'patient_portal:medical_records' %}" class="text-medical-600 hover:text-medical-800 text-sm font-medium">
                        View All →
                    </a>
                </div>
        
        {% if recent_records %}
        <div class="space-y-3">
            {% for record in recent_records %}
            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <h3 class="font-medium text-gray-900">{{ record.diagnosis|truncatechars:50 }}</h3>
                        <p class="text-sm text-gray-600">
                            <i class="fas fa-user-md mr-1 text-medical-600"></i>{{ record.doctor_name }}
                        </p>
                        <p class="text-sm text-gray-500">
                            <i class="fas fa-calendar mr-1 text-medical-600"></i>{{ record.date|date:"M d, Y" }}
                        </p>
                    </div>
                    <a href="{% url 'patient_portal:medical_record_detail' record.id %}"
                       class="text-medical-600 hover:text-medical-800">
                        <i class="fas fa-eye"></i>
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-8 text-gray-500">
            <i class="fas fa-file-medical text-4xl mb-4"></i>
            <p>No medical records found</p>
        </div>
        {% endif %}
    </div>

            <!-- Upcoming Appointments -->
            <div class="content-card">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-calendar-check text-medical-600 mr-2"></i>
                        Upcoming Appointments
                    </h2>
                    <a href="{% url 'patient_portal:appointments' %}" class="text-medical-600 hover:text-medical-800 text-sm font-medium">
                        View All →
                    </a>
                </div>
        
        {% if upcoming_appointments %}
        <div class="space-y-3">
            {% for appointment in upcoming_appointments %}
            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <h3 class="font-medium text-gray-900">{{ appointment.appointment_type|title }}</h3>
                        <p class="text-sm text-gray-600">
                            <i class="fas fa-user-md mr-1 text-medical-600"></i>{{ appointment.doctor_name }}
                        </p>
                        <p class="text-sm text-gray-500">
                            <i class="fas fa-calendar mr-1 text-medical-600"></i>{{ appointment.date|date:"M d, Y" }}
                            <i class="fas fa-clock ml-3 mr-1 text-medical-600"></i>{{ appointment.time|time:"g:i A" }}
                        </p>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                   {% if appointment.status == 'scheduled' %}bg-health-100 text-health-800
                                   {% elif appointment.status == 'confirmed' %}bg-medical-100 text-medical-800
                                   {% else %}bg-gray-100 text-gray-800{% endif %}">
                            {{ appointment.get_status_display }}
                        </span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-8 text-gray-500">
            <i class="fas fa-calendar-check text-4xl mb-4"></i>
            <p>No upcoming appointments</p>
        </div>
        {% endif %}
    </div>

    <!-- Active Prescriptions -->
    <div class="patient-card">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-pills text-blue-600 mr-2"></i>
                Active Prescriptions
            </h2>
            <a href="{% url 'patient_portal:prescriptions' %}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                View All →
            </a>
        </div>
        
        {% if active_prescriptions %}
        <div class="space-y-3">
            {% for prescription in active_prescriptions %}
            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <h3 class="font-medium text-gray-900">{{ prescription.medication }}</h3>
                        <p class="text-sm text-gray-600">{{ prescription.dosage }} - {{ prescription.frequency }}</p>
                        <p class="text-sm text-gray-500">
                            <i class="fas fa-calendar mr-1"></i>Started: {{ prescription.start_date|date:"M d, Y" }}
                        </p>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Active
                        </span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-8 text-gray-500">
            <i class="fas fa-pills text-4xl mb-4"></i>
            <p>No active prescriptions</p>
        </div>
        {% endif %}
    </div>

    <!-- Quick Actions -->
    <div class="patient-card">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">
            <i class="fas fa-bolt text-blue-600 mr-2"></i>
            Quick Actions
        </h2>
        
        <div class="space-y-3">
            <a href="{% url 'patient_portal:medical_records' %}" 
               class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-colors">
                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-file-medical text-blue-600"></i>
                </div>
                <div>
                    <p class="font-medium text-gray-900">View Medical History</p>
                    <p class="text-sm text-gray-500">Complete medical records</p>
                </div>
            </a>
            
            <a href="{% url 'patient_portal:prescriptions' %}" 
               class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-colors">
                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-pills text-blue-600"></i>
                </div>
                <div>
                    <p class="font-medium text-gray-900">Prescription History</p>
                    <p class="text-sm text-gray-500">All medications prescribed</p>
                </div>
            </a>
            
            <a href="{% url 'patient_portal:doctors' %}" 
               class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-colors">
                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-user-md text-blue-600"></i>
                </div>
                <div>
                    <p class="font-medium text-gray-900">My Healthcare Team</p>
                    <p class="text-sm text-gray-500">Doctors and specialists</p>
                </div>
            </a>
            
            <a href="{% url 'patient_portal:profile' %}" 
               class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-colors">
                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-user-cog text-blue-600"></i>
                </div>
                <div>
                    <p class="font-medium text-gray-900">Update Profile</p>
                    <p class="text-sm text-gray-500">Contact information</p>
                </div>
            </a>
        </div>
    </div>
</div>

<!-- Patient Information Summary -->
<div class="mt-8 patient-card">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">
        <i class="fas fa-user text-blue-600 mr-2"></i>
        My Information
    </h2>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div class="space-y-2">
            <p class="text-sm font-medium text-gray-500">Full Name</p>
            <p class="text-gray-900">{{ patient.full_name }}</p>
        </div>
        
        <div class="space-y-2">
            <p class="text-sm font-medium text-gray-500">Date of Birth</p>
            <p class="text-gray-900">{{ patient.date_of_birth|date:"M d, Y"|default:"Not specified" }}</p>
        </div>
        
        <div class="space-y-2">
            <p class="text-sm font-medium text-gray-500">Age</p>
            <p class="text-gray-900">{{ patient.age|default:"Not calculated" }} years</p>
        </div>
        
        <div class="space-y-2">
            <p class="text-sm font-medium text-gray-500">Gender</p>
            <p class="text-gray-900">{{ patient.get_gender_display|default:"Not specified" }}</p>
        </div>
        
        <div class="space-y-2">
            <p class="text-sm font-medium text-gray-500">Blood Type</p>
            <p class="text-gray-900">{{ patient.blood_type|default:"Unknown" }}</p>
        </div>
        
        <div class="space-y-2">
            <p class="text-sm font-medium text-gray-500">Phone Number</p>
            <p class="text-gray-900">{{ patient.phone_number|default:"Not provided" }}</p>
        </div>
        </div>

        <!-- Important Notice -->
        <div class="mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div class="flex items-start space-x-3">
                <i class="fas fa-exclamation-triangle text-yellow-600 mt-1"></i>
                <div>
                    <h3 class="text-sm font-medium text-yellow-800">Important Notice</h3>
                    <p class="text-sm text-yellow-700 mt-1">
                        This portal provides read-only access to your medical information.
                        To schedule appointments, request prescription refills, or make changes to your medical records,
                        please contact your healthcare provider directly.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
