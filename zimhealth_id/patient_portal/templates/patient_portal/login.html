{% extends 'patient_portal/base.html' %}
{% load static %}

{% block title %}Patient Login - ZimHealth-ID{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- Professional Header -->
        <div class="text-center">
            <div class="mx-auto h-16 w-16 bg-medical-600 rounded-full flex items-center justify-center mb-4">
                <i class="fas fa-user-shield text-white text-2xl"></i>
            </div>
            <h2 class="text-3xl font-bold text-gray-900 mb-2">Patient Portal Login</h2>
            <p class="text-gray-600">Access your medical information securely</p>
            <div class="read-only-badge mt-3">
                <i class="fas fa-eye mr-1"></i>
                Read-Only Access
            </div>
        </div>

        <!-- Professional Login Form -->
        <div class="auth-form-container">
            <form method="post" class="space-y-6">
                {% csrf_token %}
            
            <div class="space-y-4">
                <!-- ZimHealth-ID Field -->
                <div>
                    <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        ZimHealth-ID
                    </label>
                    <div class="flex gap-3">
                        <div class="relative flex-1">
                            <input type="text" name="{{ form.username.name }}" id="{{ form.username.id_for_label }}"
                                   class="auth-input pl-10" placeholder="Enter your ZimHealth-ID"
                                   value="{{ form.username.value|default:'' }}" required>
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                <i class="fas fa-id-card text-gray-400"></i>
                            </div>
                        </div>
                        <button type="button" id="qr-scanner-btn" class="government-filter-button flex items-center space-x-2">
                            <i class="fas fa-qrcode"></i>
                            <span>Scan QR</span>
                        </button>
                    </div>
                    {% if form.username.help_text %}
                    <p class="mt-1 text-sm text-gray-500">{{ form.username.help_text }}</p>
                    {% endif %}
                    {% if form.username.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.username.errors %}
                        <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <!-- Password Field -->
                <div>
                    <label for="{{ form.password.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Password
                    </label>
                    <div class="relative">
                        <input type="password" name="{{ form.password.name }}" id="{{ form.password.id_for_label }}"
                               class="auth-input pl-10" placeholder="Enter your password" required>
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                    </div>
                    {% if form.password.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.password.errors %}
                        <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Submit Button -->
            <div>
                <button type="submit" class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-medical-600 hover:bg-medical-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-medical-500 transition-colors duration-200">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <i class="fas fa-sign-in-alt text-medical-500 group-hover:text-medical-400"></i>
                    </span>
                    Access My Medical Information
                </button>
            </div>

            <!-- Links -->
            <div class="text-center space-y-2">
                <p class="text-sm text-gray-600">
                    Don't have an account?
                    <a href="{% url 'patient_portal:register' %}" class="font-medium text-medical-600 hover:text-medical-500">
                        Register here
                    </a>
                </p>
                <p class="text-sm text-gray-500">
                    Healthcare Provider?
                    <a href="{% url 'zhid_auth:login' %}" class="font-medium text-gray-700 hover:text-gray-600">
                        Provider Login
                    </a>
                </p>
            </div>
            </form>
        </div>

        <!-- Security Notice -->
        <div class="mt-8 p-4 bg-gray-100 rounded-lg border border-gray-200">
            <div class="flex items-start space-x-3">
                <i class="fas fa-shield-alt text-green-600 mt-1"></i>
                <div>
                    <h4 class="text-sm font-medium text-gray-900">Secure Access</h4>
                    <p class="text-sm text-gray-600 mt-1">
                        Your medical information is protected with bank-level security. 
                        You can only view your own data - no changes can be made through this portal.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- QR Code Scanner Modal -->
<div id="qr-modal" class="qr-scanner-overlay">
    <div class="qr-scanner-modal">
        <div class="qr-modal-header">
            <div class="qr-header-content">
                <div class="qr-success-indicator">
                    <div class="qr-icon-container">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <div>
                        <h3 class="qr-modal-title">Scan Your ZimHealth-ID QR Code</h3>
                        <p class="qr-modal-subtitle">Position your patient card QR code within camera view</p>
                    </div>
                </div>
            </div>
            <button type="button" id="close-qr-btn" class="qr-close-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="qr-modal-body">
            <div class="qr-video-container">
                <video id="qr-video" autoplay playsinline></video>
                <div class="qr-overlay">
                    <div class="qr-scanner-frame"></div>
                </div>
            </div>
            <div class="qr-status" id="qr-status">
                <i class="fas fa-qrcode"></i>
                <span>Position QR code within the frame</span>
            </div>
        </div>
    </div>
</div>

<style>
/* QR Scanner Modal - Matching Dashboard Patients Page Modal Size */
.qr-scanner-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.6) !important;
    backdrop-filter: blur(8px) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 9999 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
}

.qr-scanner-overlay.active {
    opacity: 1 !important;
    visibility: visible !important;
}

.qr-scanner-modal {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95)) !important;
    backdrop-filter: blur(25px) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 16px !important;
    padding: 2rem !important;
    max-width: 500px !important; /* Match dashboard patients modal size */
    width: 90% !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2) !important;
    transform: scale(0.9) !important;
    transition: transform 0.3s ease !important;
}

.qr-scanner-overlay.active .qr-scanner-modal {
    transform: scale(1) !important;
}

.qr-modal-header {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    margin-bottom: 1.5rem !important;
    padding-bottom: 1rem !important;
    border-bottom: 1px solid rgba(229, 231, 235, 0.3) !important;
}

.qr-header-content {
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important;
}

.qr-success-indicator {
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
}

.qr-icon-container {
    width: 2.5rem !important;
    height: 2.5rem !important;
    background: linear-gradient(135deg, #0284c7, #0369a1) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-size: 1.25rem !important;
}

.qr-modal-title {
    font-size: 1.125rem !important;
    font-weight: 600 !important;
    color: #111827 !important;
    margin: 0 !important;
}

.qr-modal-subtitle {
    font-size: 0.875rem !important;
    color: #6b7280 !important;
    margin: 0.25rem 0 0 0 !important;
}

.qr-close-btn {
    background: none !important;
    border: none !important;
    color: #6b7280 !important;
    cursor: pointer !important;
    padding: 0.5rem !important;
    border-radius: 0.375rem !important;
    transition: all 0.2s ease !important;
    font-size: 1.25rem !important;
}

.qr-close-btn:hover {
    background: rgba(107, 114, 128, 0.1) !important;
    color: #374151 !important;
}

.qr-modal-body {
    text-align: center !important;
}

.qr-video-container {
    position: relative !important;
    margin-bottom: 1rem !important;
}

#qr-video {
    width: 100% !important;
    max-width: 400px !important; /* Limit video size to match modal */
    height: auto !important;
    border-radius: 12px !important;
    background: #000 !important;
}

.qr-overlay {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    pointer-events: none !important;
}

.qr-scanner-frame {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 200px !important;
    height: 200px !important;
    border: 3px solid #0284c7 !important;
    border-radius: 12px !important;
    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.3) !important;
}

.qr-status {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
    padding: 0.75rem !important;
    background: rgba(2, 132, 199, 0.1) !important;
    border-radius: 0.5rem !important;
    color: #0284c7 !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
}

.qr-status.success {
    background: rgba(34, 197, 94, 0.1) !important;
    color: #16a34a !important;
}

.qr-status.error {
    background: rgba(239, 68, 68, 0.1) !important;
    color: #dc2626 !important;
}

.qr-status.warning {
    background: rgba(245, 158, 11, 0.1) !important;
    color: #d97706 !important;
}

/* Responsive design */
@media (max-width: 640px) {
    .qr-scanner-modal {
        max-width: 95% !important;
        padding: 1.5rem !important;
    }
    
    #qr-video {
        max-width: 100% !important;
    }
    
    .qr-scanner-frame {
        width: 150px !important;
        height: 150px !important;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<!-- jsQR Library for QR Code Detection -->
<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>

<script>
// QR Code Scanner for Patient Login - Enhanced for better data inheritance
document.addEventListener('DOMContentLoaded', function() {
    const qrScannerBtn = document.getElementById('qr-scanner-btn');
    const closeQrBtn = document.getElementById('close-qr-btn');
    const qrModal = document.getElementById('qr-modal');

    // Ensure modal is hidden on page load
    if (qrModal) {
        qrModal.classList.remove('active');
        qrModal.style.opacity = '0';
        qrModal.style.visibility = 'hidden';
        qrModal.style.pointerEvents = 'none';
    }

    if (qrScannerBtn) {
        qrScannerBtn.addEventListener('click', function(e) {
            e.preventDefault();
            openQRScanner();
        });
    }

    if (closeQrBtn) {
        closeQrBtn.addEventListener('click', function() {
            closeQRScanner();
        });
    }

    // Close modal when clicking outside
    if (qrModal) {
        qrModal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeQRScanner();
            }
        });
    }
});

// Open QR Code Scanner
async function openQRScanner() {
    const qrModal = document.getElementById('qr-modal');
    const qrVideo = document.getElementById('qr-video');

    if (!qrModal || !qrVideo) {
        console.error('QR scanner elements not found');
        return;
    }

    try {
        // Show modal first
        qrModal.classList.add('active');
        qrModal.style.opacity = '1';
        qrModal.style.visibility = 'visible';
        qrModal.style.pointerEvents = 'auto';

        // Update status
        updateQRScannerStatus('Initializing camera...', 'info');

        const stream = await navigator.mediaDevices.getUserMedia({
            video: {
                facingMode: 'environment',
                width: { ideal: 1280, min: 640 },
                height: { ideal: 720, min: 480 },
                frameRate: { ideal: 30, min: 15 }
            }
        });

        qrVideo.srcObject = stream;
        qrVideo.play();

        // Update status
        updateQRScannerStatus('Camera ready. Point at QR code...', 'info');

        // Start QR detection
        startQRDetection(qrVideo);

    } catch (error) {
        console.error('Error accessing camera:', error);
        updateQRScannerStatus('Camera access denied. Please check permissions.', 'error');
        
        // Show user-friendly error message
        let errorMessage = 'Camera access failed. ';
        if (error.name === 'NotAllowedError') {
            errorMessage += 'Please allow camera access and try again.';
        } else if (error.name === 'NotFoundError') {
            errorMessage += 'No camera found on this device.';
        } else if (error.name === 'NotSupportedError') {
            errorMessage += 'Camera not supported on this device.';
        } else {
            errorMessage += 'Please check your camera settings and try again.';
        }
        
        showNotification(errorMessage, 'error');
        
        // Close modal after error
        setTimeout(() => {
            closeQRScanner();
        }, 3000);
    }
}

// Start QR Code Detection
function startQRDetection(video) {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    let isScanning = true;

    function scanFrame() {
        if (!isScanning) return;

        const qrModal = document.getElementById('qr-modal');
        if (!qrModal || !qrModal.classList.contains('active')) {
            isScanning = false;
            return;
        }

        if (video.readyState === video.HAVE_ENOUGH_DATA) {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0, canvas.width, canvas.height);

            const imageData = context.getImageData(0, 0, canvas.width, canvas.height);

            // Use jsQR library to detect QR codes
            if (typeof jsQR !== 'undefined') {
                const qrCode = jsQR(imageData.data, imageData.width, imageData.height, {
                    inversionAttempts: "attemptBoth",
                });

                if (qrCode && qrCode.data) {
                    console.log('QR Code detected:', qrCode.data);
                    updateQRScannerStatus('QR Code detected! Processing...', 'success');
                    isScanning = false;
                    handleQRCodeDetected(qrCode.data);
                    return;
                }

                // Update status to show active scanning
                updateQRScannerStatus('Scanning... Point camera at QR code', 'info');
            } else {
                // jsQR library not loaded - show error
                console.error('jsQR library not loaded');
                updateQRScannerStatus('QR scanner library not loaded', 'error');
                isScanning = false;
                closeQRScanner();
                showNotification('QR scanner library failed to load. Please refresh the page.', 'error');
                return;
            }
        }

        // Continue scanning
        requestAnimationFrame(scanFrame);
    }

    // Start scanning when video is ready
    video.addEventListener('loadedmetadata', () => {
        scanFrame();
    });

    // If video is already loaded, start immediately
    if (video.readyState >= video.HAVE_METADATA) {
        scanFrame();
    }

    // Stop scanning when modal is closed
    const qrModal = document.getElementById('qr-modal');
    if (qrModal) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (!qrModal.classList.contains('active')) {
                        isScanning = false;
                    }
                }
            });
        });
        observer.observe(qrModal, { attributes: true });
    }
}

// Handle QR Code Detection - Enhanced for better data inheritance
async function handleQRCodeDetected(qrData) {
    console.log('Processing QR data:', qrData);
    
    // Close scanner first
    closeQRScanner();

    try {
        // Show loading notification
        showNotification('Looking up patient information...', 'info');

        // Make AJAX request to patient portal QR endpoint
        const response = await fetch(`/patient/ajax/scan-qr/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': getCsrfToken(),
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: `qr_data=${encodeURIComponent(qrData)}`
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('QR scan response:', data);

        if (data.success) {
            // Successfully found patient data
            const patientData = data.patient_data;
            console.log('Patient data received:', patientData);
            
            // Populate the login form with ZimHealth-ID
            const usernameField = document.getElementById('id_username');
            if (usernameField) {
                usernameField.value = patientData.zimhealth_id;
                console.log(`Set username field to: ${patientData.zimhealth_id}`);
                
                // Show success notification with patient details
                showNotification(`Patient found: ${patientData.first_name} ${patientData.last_name} (${patientData.zimhealth_id})`, 'success');
                
                // Focus on password field for better UX
                const passwordField = document.getElementById('id_password');
                if (passwordField) {
                    passwordField.focus();
                    console.log('Focused on password field');
                }
                
                // Show additional patient info in console for debugging
                console.log('=== PATIENT DATA LOADED ===');
                console.log('ZimHealth ID:', patientData.zimhealth_id);
                console.log('Name:', patientData.full_name);
                console.log('Phone:', patientData.phone_number);
                console.log('Email:', patientData.email);
                console.log('Age:', patientData.age);
                console.log('Gender:', patientData.gender);
                console.log('Blood Type:', patientData.blood_type);
                console.log('Emergency Contact:', patientData.emergency_contact);
                console.log('Allergies:', patientData.allergies);
                console.log('Address:', patientData.address);
                console.log('Has Account:', patientData.has_account);
                console.log('Account Status:', patientData.account_status);
                console.log('Recent Records:', patientData.recent_records_count);
                console.log('Upcoming Appointments:', patientData.upcoming_appointments);
                console.log('========================');
                
                // Optional: Show a subtle info panel with patient details
                showPatientInfoPanel(patientData);
                
            } else {
                console.error('Username field not found in DOM');
                showNotification('Error: Username field not found', 'error');
            }
        } else {
            // Handle error from server
            const errorMessage = data.error || 'Invalid QR code or patient not found';
            showNotification(errorMessage, 'error');
            console.error('QR scan error:', data.error);
        }
    } catch (error) {
        console.error('Error processing QR code:', error);
        
        // Show user-friendly error message
        let errorMessage = 'Error processing QR code. ';
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            errorMessage += 'Network error. Please check your connection.';
        } else {
            errorMessage += 'Please try again or contact support.';
        }
        
        showNotification(errorMessage, 'error');
    }
}

// Show patient info panel for better UX
function showPatientInfoPanel(patientData) {
    // Remove existing panel if any
    const existingPanel = document.getElementById('patient-info-panel');
    if (existingPanel) {
        existingPanel.remove();
    }
    
    // Create info panel
    const panel = document.createElement('div');
    panel.id = 'patient-info-panel';
    panel.className = 'fixed top-20 right-4 z-40 bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm';
    
    // Build account status message with enhanced details
    let accountStatusHtml = '';
    if (patientData.has_account) {
        if (patientData.account_status === 'active') {
            accountStatusHtml = `
                <div class="mb-3 p-2 bg-green-50 border border-green-200 rounded">
                    <div class="flex items-center text-green-800">
                        <i class="fas fa-check-circle mr-2"></i>
                        <span class="text-sm font-medium">Account Active & Ready</span>
                    </div>
                    <div class="text-xs text-green-600 mt-1">
                        You can now login with your password
                    </div>
                </div>
            `;
        } else {
            accountStatusHtml = `
                <div class="mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
                    <div class="flex items-center text-yellow-800">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <span class="text-sm font-medium">Account Needs Attention</span>
                    </div>
                    <div class="text-xs text-yellow-600 mt-1">
                        Account exists but may need verification
                    </div>
                </div>
            `;
        }
    } else {
        accountStatusHtml = `
            <div class="mb-3 p-2 bg-blue-50 border border-blue-200 rounded">
                <div class="flex items-center text-blue-800">
                    <i class="fas fa-info-circle mr-2"></i>
                    <span class="text-sm font-medium">No Account Yet</span>
                </div>
                <div class="text-xs text-blue-600 mt-1">
                    <a href="/patient/register/" class="underline">Click here to create your account</a>
                </div>
            </div>
        `;
    }
    
    // Build enhanced user details if available
    let userDetailsHtml = '';
    if (patientData.user_details && Object.keys(patientData.user_details).length > 0) {
        const details = patientData.user_details;
        let verificationStatus = '';
        let lockStatus = '';
        
        if (details.is_verified !== undefined && details.is_verified !== 'N/A') {
            verificationStatus = `
                <div class="flex items-center ${details.is_verified ? 'text-green-600' : 'text-red-600'}">
                    <i class="fas fa-${details.is_verified ? 'check' : 'times'}-circle mr-1"></i>
                    <span class="text-xs">${details.is_verified ? 'Verified' : 'Not Verified'}</span>
                </div>
            `;
        }
        
        if (details.account_locked) {
            lockStatus = `
                <div class="flex items-center text-red-600">
                    <i class="fas fa-lock mr-1"></i>
                    <span class="text-xs">Account Locked</span>
                </div>
            `;
        } else if (details.failed_attempts > 0) {
            lockStatus = `
                <div class="flex items-center text-yellow-600">
                    <i class="fas fa-exclamation-triangle mr-1"></i>
                    <span class="text-xs">${details.failed_attempts} failed attempts</span>
                </div>
            `;
        }
        
        userDetailsHtml = `
            <div class="mt-3 p-2 bg-gray-50 border border-gray-200 rounded">
                <div class="text-xs text-gray-600 space-y-1">
                    <div><strong>Username:</strong> ${details.username || 'N/A'}</div>
                    <div><strong>Email:</strong> ${details.email || 'N/A'}</div>
                    <div><strong>Member Since:</strong> ${details.date_joined || 'N/A'}</div>
                    <div><strong>Last Login:</strong> ${details.last_login || 'N/A'}</div>
                    ${verificationStatus}
                    ${lockStatus}
                </div>
            </div>
        `;
    }
    
    // Build status message section
    let statusMessageHtml = '';
    if (patientData.status_message) {
        statusMessageHtml = `
            <div class="mt-3 p-2 bg-gray-50 border border-gray-200 rounded">
                <div class="text-xs text-gray-700">
                    <i class="fas fa-info-circle mr-1"></i>
                    ${patientData.status_message}
                </div>
            </div>
        `;
    }
    
    panel.innerHTML = `
        <div class="flex items-center justify-between mb-3">
            <h4 class="text-sm font-semibold text-gray-900">Patient Information</h4>
            <button onclick="this.parentElement.parentElement.remove()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        ${accountStatusHtml}
        
        <div class="space-y-2 text-xs text-gray-600">
            <div><strong>Name:</strong> ${patientData.full_name}</div>
            <div><strong>ID:</strong> ${patientData.zimhealth_id}</div>
            <div><strong>Age:</strong> ${patientData.age || 'N/A'}</div>
            <div><strong>Phone:</strong> ${patientData.phone_number || 'N/A'}</div>
            <div><strong>Blood Type:</strong> ${patientData.blood_type || 'N/A'}</div>
            ${patientData.recent_records_count > 0 ? `<div><strong>Recent Records:</strong> ${patientData.recent_records_count}</div>` : ''}
            ${patientData.upcoming_appointments > 0 ? `<div><strong>Upcoming Appointments:</strong> ${patientData.upcoming_appointments}</div>` : ''}
        </div>
        
        ${userDetailsHtml}
        ${statusMessageHtml}
        
        <div class="mt-3 text-xs text-gray-500">
            <i class="fas fa-info-circle mr-1"></i>
            ${patientData.has_account ? 
                (patientData.account_status === 'active' ? 
                    'Your account is ready. Enter your password above to login.' : 
                    'Your account exists but may need verification. Try logging in or contact support.'
                ) : 
                'You need to create an account to access your medical information.'
            }
        </div>
    `;
    
    document.body.appendChild(panel);
    
    // Auto-remove after 20 seconds (increased for better readability)
    setTimeout(() => {
        if (panel.parentElement) {
            panel.remove();
        }
    }, 20000);
}

// Close QR Code Scanner
function closeQRScanner() {
    const qrModal = document.getElementById('qr-modal');
    const qrVideo = document.getElementById('qr-video');

    // Stop video stream
    if (qrVideo && qrVideo.srcObject) {
        const tracks = qrVideo.srcObject.getTracks();
        tracks.forEach(track => track.stop());
        qrVideo.srcObject = null;
    }

    // Hide modal
    if (qrModal) {
        qrModal.classList.remove('active');
        qrModal.style.opacity = '0';
        qrModal.style.visibility = 'hidden';
        qrModal.style.pointerEvents = 'none';
    }
}

// Helper functions
function updateQRScannerStatus(message, type) {
    const statusElement = document.getElementById('qr-status');
    if (statusElement) {
        const icon = type === 'success' ? 'fa-check-circle' :
                    type === 'error' ? 'fa-exclamation-triangle' :
                    type === 'warning' ? 'fa-exclamation-circle' : 'fa-qrcode';

        statusElement.innerHTML = `<i class="fas ${icon}"></i><span>${message}</span>`;
        statusElement.className = `qr-status ${type}`;
    }
}

function getCsrfToken() {
    const token = document.querySelector('[name=csrfmiddlewaretoken]');
    return token ? token.value : '';
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transition-all duration-300 transform translate-x-full`;
    
    const colors = {
        'success': 'bg-green-500 text-white',
        'error': 'bg-red-500 text-white',
        'warning': 'bg-yellow-500 text-white',
        'info': 'bg-blue-500 text-white'
    };
    
    notification.className += ` ${colors[type] || colors.info}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Animate out and remove
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 4000);
}
</script>
{% endblock %}
