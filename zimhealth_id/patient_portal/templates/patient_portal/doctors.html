{% extends 'patient_portal/base.html' %}
{% load static %}

{% block title %}My Healthcare Team - ZimHealth-ID{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="patient-header">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold">My Healthcare Team</h1>
            <p class="text-blue-100">Doctors and healthcare providers you've visited</p>
        </div>
        <div class="read-only-badge bg-white bg-opacity-20 text-white border-white">
            <i class="fas fa-eye mr-1"></i>
            Read-Only Access
        </div>
    </div>
</div>

<!-- Summary -->
<div class="patient-card mb-6">
    <div class="flex items-center justify-between">
        <div>
            <h2 class="text-lg font-semibold text-gray-900">Healthcare Providers</h2>
            <p class="text-gray-600">You have visited {{ total_doctors }} healthcare provider{{ total_doctors|pluralize }}</p>
        </div>
        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <i class="fas fa-user-md text-blue-600 text-xl"></i>
        </div>
    </div>
</div>

<!-- Doctors List -->
{% if doctors %}
<div class="space-y-4">
    {% for doctor in doctors %}
    <div class="patient-card hover:shadow-lg transition-shadow">
        <div class="flex items-start justify-between">
            <div class="flex-1">
                <!-- Doctor Header -->
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-user-md text-blue-600 mr-2"></i>
                        {{ doctor.doctor_name }}
                    </h3>
                </div>
                
                <!-- Doctor Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="space-y-2">
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-hospital w-4 mr-2"></i>
                            <span class="font-medium">Facility:</span>
                            <span class="ml-1">{{ doctor.facility_name }}</span>
                        </div>
                        
                        {% if doctor.visit_count > 0 %}
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-file-medical w-4 mr-2"></i>
                            <span class="font-medium">Medical Visits:</span>
                            <span class="ml-1">{{ doctor.visit_count }} visit{{ doctor.visit_count|pluralize }}</span>
                        </div>
                        {% endif %}
                        
                        {% if doctor.appointment_count > 0 %}
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-calendar-check w-4 mr-2"></i>
                            <span class="font-medium">Appointments:</span>
                            <span class="ml-1">{{ doctor.appointment_count }} appointment{{ doctor.appointment_count|pluralize }}</span>
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="space-y-2">
                        {% if doctor.last_visit %}
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-calendar w-4 mr-2"></i>
                            <span class="font-medium">Last Visit:</span>
                            <span class="ml-1">{{ doctor.last_visit|date:"M d, Y" }}</span>
                        </div>
                        {% endif %}
                        
                        {% if doctor.last_appointment %}
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-calendar-alt w-4 mr-2"></i>
                            <span class="font-medium">Last Appointment:</span>
                            <span class="ml-1">{{ doctor.last_appointment|date:"M d, Y" }}</span>
                        </div>
                        {% endif %}
                        
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-chart-line w-4 mr-2"></i>
                            <span class="font-medium">Total Interactions:</span>
                            <span class="ml-1">{{ doctor.visit_count|add:doctor.appointment_count }}</span>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="flex flex-wrap gap-2">
                    {% if doctor.visit_count > 0 %}
                    <a href="{% url 'patient_portal:medical_records' %}?search={{ doctor.doctor_name }}" 
                       class="inline-flex items-center px-3 py-1 border border-blue-300 rounded-md text-xs font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors">
                        <i class="fas fa-file-medical mr-1"></i>
                        View Medical Records
                    </a>
                    {% endif %}
                    
                    {% if doctor.appointment_count > 0 %}
                    <a href="{% url 'patient_portal:appointments' %}?search={{ doctor.doctor_name }}" 
                       class="inline-flex items-center px-3 py-1 border border-green-300 rounded-md text-xs font-medium text-green-700 bg-green-50 hover:bg-green-100 transition-colors">
                        <i class="fas fa-calendar-check mr-1"></i>
                        View Appointments
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

{% else %}
<!-- Empty State -->
<div class="patient-card text-center py-12">
    <i class="fas fa-user-md text-6xl text-gray-300 mb-4"></i>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No Healthcare Providers Found</h3>
    <p class="text-gray-500">You haven't visited any healthcare providers yet or no records are available.</p>
</div>
{% endif %}

<!-- Contact Information -->
<div class="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
    <div class="flex items-start space-x-3">
        <i class="fas fa-phone text-blue-600 mt-1"></i>
        <div>
            <h3 class="text-sm font-medium text-blue-800">Need to Contact a Provider?</h3>
            <p class="text-sm text-blue-700 mt-1">
                To schedule appointments, ask questions, or request services from any of your healthcare providers, 
                please contact them directly using the contact information provided during your visits.
            </p>
        </div>
    </div>
</div>
{% endblock %}
