{% extends 'patient_portal/base.html' %}
{% load static %}

{% block title %}My Profile - ZimHealth-ID{% endblock %}

{% block content %}
<div class="page-container">
    <!-- Professional Page Header -->
    <div class="page-header">
        <div class="flex justify-between items-start">
            <!-- Left Section: Clean Professional Title -->
            <div>
                <h1 class="page-title">My Profile</h1>
                <p class="page-subtitle">View and update your contact information</p>
            </div>

            <!-- Right Section: Status Badge -->
            <div class="read-only-badge">
                <i class="fas fa-user-cog mr-1"></i>
                Limited Updates
            </div>
        </div>
    </div>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- Patient Information (Read-Only) -->
    <div class="content-card">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">
            <i class="fas fa-id-card text-blue-600 mr-2"></i>
            Patient Information
        </h2>
        
        <div class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-2">
                    <p class="text-sm font-medium text-gray-500">ZimHealth-ID</p>
                    <p class="text-gray-900 font-mono">{{ patient.zimhealth_id }}</p>
                </div>
                
                <div class="space-y-2">
                    <p class="text-sm font-medium text-gray-500">Registration Date</p>
                    <p class="text-gray-900">{{ patient.created_at|date:"M d, Y" }}</p>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-2">
                    <p class="text-sm font-medium text-gray-500">Full Name</p>
                    <p class="text-gray-900">{{ patient.full_name }}</p>
                </div>
                
                <div class="space-y-2">
                    <p class="text-sm font-medium text-gray-500">Date of Birth</p>
                    <p class="text-gray-900">{{ patient.date_of_birth|date:"M d, Y"|default:"Not specified" }}</p>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-2">
                    <p class="text-sm font-medium text-gray-500">Gender</p>
                    <p class="text-gray-900">{{ patient.get_gender_display|default:"Not specified" }}</p>
                </div>
                
                <div class="space-y-2">
                    <p class="text-sm font-medium text-gray-500">Blood Type</p>
                    <p class="text-gray-900">{{ patient.blood_type|default:"Unknown" }}</p>
                </div>
            </div>
            
            {% if patient.allergies %}
            <div class="space-y-2">
                <p class="text-sm font-medium text-gray-500">Known Allergies</p>
                <p class="text-gray-900">{{ patient.allergies }}</p>
            </div>
            {% endif %}
            
            {% if patient.emergency_contact_name %}
            <div class="space-y-2">
                <p class="text-sm font-medium text-gray-500">Emergency Contact</p>
                <p class="text-gray-900">{{ patient.emergency_contact_name }}</p>
                {% if patient.emergency_contact_phone %}
                <p class="text-gray-600 text-sm">{{ patient.emergency_contact_phone }}</p>
                {% endif %}
            </div>
            {% endif %}
        </div>
        
        <div class="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <div class="flex items-start space-x-3">
                <i class="fas fa-info-circle text-gray-600 mt-1"></i>
                <div>
                    <h4 class="text-sm font-medium text-gray-900">Update Medical Information</h4>
                    <p class="text-sm text-gray-600 mt-1">
                        To update your medical information, allergies, or emergency contacts, 
                        please contact your healthcare provider during your next visit.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Information (Editable) -->
    <div class="patient-card">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">
            <i class="fas fa-address-book text-blue-600 mr-2"></i>
            Contact Information
        </h2>
        
        <form method="post" class="space-y-4">
            {% csrf_token %}
            
            <!-- Email -->
            <div>
                <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Email Address
                </label>
                <div class="relative">
                    {{ form.email }}
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                        <i class="fas fa-envelope text-gray-400"></i>
                    </div>
                </div>
                {% if form.email.errors %}
                <div class="mt-1 text-sm text-red-600">
                    {% for error in form.email.errors %}
                    <p>{{ error }}</p>
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <!-- Phone Number -->
            <div>
                <label for="{{ form.phone_number.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number
                </label>
                <div class="relative">
                    {{ form.phone_number }}
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                        <i class="fas fa-phone text-gray-400"></i>
                    </div>
                </div>
                {% if form.phone_number.errors %}
                <div class="mt-1 text-sm text-red-600">
                    {% for error in form.phone_number.errors %}
                    <p>{{ error }}</p>
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <!-- Submit Button -->
            <div class="pt-4">
                <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>
                    Update Contact Information
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Account Information -->
<div class="mt-8 patient-card">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">
        <i class="fas fa-shield-alt text-blue-600 mr-2"></i>
        Account Security
    </h2>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-4">
            <div class="space-y-2">
                <p class="text-sm font-medium text-gray-500">Account Status</p>
                <div class="flex items-center">
                    {% if patient_user.is_verified %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <i class="fas fa-check-circle mr-1"></i>Verified
                    </span>
                    {% else %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        <i class="fas fa-exclamation-circle mr-1"></i>Pending Verification
                    </span>
                    {% endif %}
                </div>
            </div>
            
            <div class="space-y-2">
                <p class="text-sm font-medium text-gray-500">Registration Date</p>
                <p class="text-gray-900">{{ patient_user.registration_date|date:"M d, Y g:i A" }}</p>
            </div>
            
            <div class="space-y-2">
                <p class="text-sm font-medium text-gray-500">Last Login</p>
                <p class="text-gray-900">{{ patient_user.last_login_date|date:"M d, Y g:i A"|default:"First time login" }}</p>
            </div>
        </div>
        
        <div class="space-y-4">
            <div class="space-y-2">
                <p class="text-sm font-medium text-gray-500">Terms Accepted</p>
                <div class="flex items-center">
                    {% if patient_user.terms_accepted %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <i class="fas fa-check mr-1"></i>Accepted
                    </span>
                    <span class="text-xs text-gray-500 ml-2">{{ patient_user.terms_accepted_date|date:"M d, Y" }}</span>
                    {% else %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <i class="fas fa-times mr-1"></i>Not Accepted
                    </span>
                    {% endif %}
                </div>
            </div>
            
            <div class="space-y-2">
                <p class="text-sm font-medium text-gray-500">Failed Login Attempts</p>
                <p class="text-gray-900">{{ patient_user.failed_login_attempts }}</p>
            </div>
            
            {% if patient_user.account_locked_until %}
            <div class="space-y-2">
                <p class="text-sm font-medium text-red-500">Account Locked Until</p>
                <p class="text-red-700">{{ patient_user.account_locked_until|date:"M d, Y g:i A" }}</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

        <!-- Security Notice -->
        <div class="mt-8 p-6 bg-health-50 border border-health-200 rounded-lg">
            <div class="flex items-start space-x-3">
                <i class="fas fa-shield-alt text-health-600 mt-1"></i>
                <div>
                    <h3 class="text-sm font-medium text-health-800">Data Security</h3>
                    <p class="text-sm text-health-700 mt-1">
                        Your medical information is protected with bank-level security. Only you can access your data,
                        and all access is logged for security purposes. We never share your information without your explicit consent.
                    </p>
                </div>
            </div>
        </div>

        <!-- Help Section -->
        <div class="mt-6 p-6 bg-gray-50 border border-gray-200 rounded-lg">
            <div class="flex items-start space-x-3">
                <i class="fas fa-question-circle text-gray-600 mt-1"></i>
                <div>
                    <h3 class="text-sm font-medium text-gray-900">Need Help?</h3>
                    <p class="text-sm text-gray-600 mt-1">
                        If you have questions about your account or need assistance accessing your medical information,
                        please contact your healthcare provider or the facility where you receive care.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
