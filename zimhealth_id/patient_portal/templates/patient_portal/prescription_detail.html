{% extends 'patient_portal/base.html' %}
{% load static %}

{% block title %}Prescription Details - ZimHealth-ID{% endblock %}

{% block content %}
<!-- Breadcrumb -->
<nav class="mb-6">
    <ol class="flex items-center space-x-2 text-sm text-gray-500">
        <li><a href="{% url 'patient_portal:dashboard' %}" class="hover:text-blue-600">Dashboard</a></li>
        <li><i class="fas fa-chevron-right"></i></li>
        <li><a href="{% url 'patient_portal:prescriptions' %}" class="hover:text-blue-600">Prescriptions</a></li>
        <li><i class="fas fa-chevron-right"></i></li>
        <li class="text-gray-900">Prescription Details</li>
    </ol>
</nav>

<!-- Page Header -->
<div class="patient-header">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold">{{ prescription.medication }}</h1>
            <p class="text-blue-100">Prescribed on {{ prescription.start_date|date:"F d, Y" }}</p>
        </div>
        <div class="text-right">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white bg-opacity-20 text-white border border-white border-opacity-30
                       {% if prescription.status == 'active' %}bg-green-100 text-green-800 border-green-200
                       {% elif prescription.status == 'completed' %}bg-gray-100 text-gray-800 border-gray-200
                       {% elif prescription.status == 'discontinued' %}bg-red-100 text-red-800 border-red-200
                       {% else %}bg-yellow-100 text-yellow-800 border-yellow-200{% endif %}">
                {{ prescription.get_status_display }}
            </span>
            <div class="read-only-badge bg-white bg-opacity-20 text-white border-white mt-2">
                <i class="fas fa-eye mr-1"></i>
                Read-Only Access
            </div>
        </div>
    </div>
</div>

<!-- Prescription Information -->
<div class="patient-card">
    <h2 class="text-lg font-semibold text-gray-900 mb-6">
        <i class="fas fa-pills text-blue-600 mr-2"></i>
        Prescription Information
    </h2>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Medication Details -->
        <div class="space-y-4">
            <h3 class="text-md font-medium text-gray-900 border-b border-gray-200 pb-2">Medication Details</h3>
            
            <div class="space-y-3">
                <div class="flex items-start">
                    <i class="fas fa-pills w-5 mr-3 text-gray-400 mt-1"></i>
                    <div>
                        <span class="font-medium text-gray-700">Medication:</span>
                        <p class="text-gray-900">{{ prescription.medication }}</p>
                    </div>
                </div>
                
                <div class="flex items-start">
                    <i class="fas fa-weight w-5 mr-3 text-gray-400 mt-1"></i>
                    <div>
                        <span class="font-medium text-gray-700">Dosage:</span>
                        <p class="text-gray-900">{{ prescription.dosage }}</p>
                    </div>
                </div>
                
                <div class="flex items-start">
                    <i class="fas fa-clock w-5 mr-3 text-gray-400 mt-1"></i>
                    <div>
                        <span class="font-medium text-gray-700">Frequency:</span>
                        <p class="text-gray-900">{{ prescription.frequency }}</p>
                    </div>
                </div>
                
                {% if prescription.duration %}
                <div class="flex items-start">
                    <i class="fas fa-hourglass-half w-5 mr-3 text-gray-400 mt-1"></i>
                    <div>
                        <span class="font-medium text-gray-700">Duration:</span>
                        <p class="text-gray-900">{{ prescription.duration }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Timeline -->
        <div class="space-y-4">
            <h3 class="text-md font-medium text-gray-900 border-b border-gray-200 pb-2">Timeline</h3>
            
            <div class="space-y-3">
                <div class="flex items-start">
                    <i class="fas fa-play w-5 mr-3 text-green-500 mt-1"></i>
                    <div>
                        <span class="font-medium text-gray-700">Start Date:</span>
                        <p class="text-gray-900">{{ prescription.start_date|date:"F d, Y" }}</p>
                    </div>
                </div>
                
                {% if prescription.end_date %}
                <div class="flex items-start">
                    <i class="fas fa-stop w-5 mr-3 text-red-500 mt-1"></i>
                    <div>
                        <span class="font-medium text-gray-700">End Date:</span>
                        <p class="text-gray-900">{{ prescription.end_date|date:"F d, Y" }}</p>
                    </div>
                </div>
                {% endif %}
                
                <div class="flex items-start">
                    <i class="fas fa-plus w-5 mr-3 text-blue-500 mt-1"></i>
                    <div>
                        <span class="font-medium text-gray-700">Prescribed:</span>
                        <p class="text-gray-900">{{ prescription.created_at|date:"F d, Y g:i A" }}</p>
                    </div>
                </div>
                
                {% if prescription.updated_at != prescription.created_at %}
                <div class="flex items-start">
                    <i class="fas fa-edit w-5 mr-3 text-yellow-500 mt-1"></i>
                    <div>
                        <span class="font-medium text-gray-700">Last Updated:</span>
                        <p class="text-gray-900">{{ prescription.updated_at|date:"F d, Y g:i A" }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Instructions -->
{% if prescription.instructions %}
<div class="mt-6 patient-card">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">
        <i class="fas fa-info-circle text-blue-600 mr-2"></i>
        Instructions
    </h2>
    <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <p class="text-gray-800 leading-relaxed">{{ prescription.instructions }}</p>
    </div>
</div>
{% endif %}

<!-- Related Medical Record -->
<div class="mt-6 patient-card">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">
        <i class="fas fa-file-medical text-blue-600 mr-2"></i>
        Related Medical Record
    </h2>
    
    <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
        <div class="flex items-center justify-between">
            <div class="flex-1">
                <h3 class="font-medium text-gray-900">{{ prescription.medical_record.diagnosis }}</h3>
                <p class="text-sm text-gray-600">
                    <i class="fas fa-calendar mr-1"></i>{{ prescription.medical_record.date|date:"M d, Y" }}
                    <i class="fas fa-user-md ml-3 mr-1"></i>{{ prescription.medical_record.doctor_name }}
                </p>
            </div>
            <a href="{% url 'patient_portal:medical_record_detail' prescription.medical_record.id %}" 
               class="text-blue-600 hover:text-blue-800">
                <i class="fas fa-eye mr-1"></i>View Record
            </a>
        </div>
    </div>
</div>

<!-- Important Notice -->
<div class="mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
    <div class="flex items-start space-x-3">
        <i class="fas fa-exclamation-triangle text-yellow-600 mt-1"></i>
        <div>
            <h3 class="text-sm font-medium text-yellow-800">Important Medication Information</h3>
            <ul class="text-sm text-yellow-700 mt-2 space-y-1">
                <li>• Always follow the prescribed dosage and frequency</li>
                <li>• Do not stop taking medication without consulting your doctor</li>
                <li>• Contact your healthcare provider if you experience side effects</li>
                <li>• For prescription refills, contact your healthcare provider directly</li>
            </ul>
        </div>
    </div>
</div>

<!-- Navigation -->
<div class="mt-8 flex justify-between">
    <a href="{% url 'patient_portal:prescriptions' %}" 
       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
        <i class="fas fa-arrow-left mr-2"></i>
        Back to Prescriptions
    </a>
    
    <a href="{% url 'patient_portal:dashboard' %}" 
       class="inline-flex items-center px-4 py-2 border border-blue-300 rounded-md text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors">
        <i class="fas fa-tachometer-alt mr-2"></i>
        Return to Dashboard
    </a>
</div>
{% endblock %}
