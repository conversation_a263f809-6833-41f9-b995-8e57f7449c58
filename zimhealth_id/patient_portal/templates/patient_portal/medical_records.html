{% extends 'patient_portal/base.html' %}
{% load static %}

{% block title %}My Medical Records - ZimHealth-ID{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Professional Page Header -->
    <div class="dashboard-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="header-content">
                <!-- Left Section: Clean Professional Title -->
                <div class="header-left">
                    <h1 class="header-title">My Medical Records</h1>
                    <p class="header-subtitle">Complete history of your medical visits and diagnoses</p>
                </div>

                <!-- Right Section: Status Badge -->
                <div class="header-right">
                    <div class="read-only-badge">
                        <i class="fas fa-eye mr-1"></i>
                        Read-Only Access
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Professional Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">

<!-- Search and Filters -->
<div class="patient-card mb-6">
    <form method="get" class="flex flex-col md:flex-row gap-4">
        <div class="flex-1">
            <div class="relative">
                <input type="text" name="search" value="{{ search_query }}" 
                       class="government-search-input-compact w-full pl-10"
                       placeholder="Search medical records by diagnosis, symptoms, or doctor">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
            </div>
        </div>
        <button type="submit" class="government-filter-button">
            <i class="fas fa-search mr-2"></i>Search
        </button>
        {% if search_query %}
        <a href="{% url 'patient_portal:medical_records' %}" class="government-filter-button bg-gray-500 hover:bg-gray-600">
            <i class="fas fa-times mr-2"></i>Clear
        </a>
        {% endif %}
    </form>
</div>

<!-- Results Summary -->
<div class="mb-6">
    <p class="text-gray-600">
        {% if search_query %}
        Showing {{ medical_records|length }} of {{ total_records }} medical records for "{{ search_query }}"
        {% else %}
        Showing all {{ total_records }} medical records
        {% endif %}
    </p>
</div>

<!-- Medical Records List -->
{% if medical_records %}
<div class="space-y-4">
    {% for record in medical_records %}
    <div class="patient-card hover:shadow-lg transition-shadow">
        <div class="flex items-start justify-between">
            <div class="flex-1">
                <!-- Record Header -->
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-lg font-semibold text-gray-900">{{ record.diagnosis }}</h3>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                               {% if record.status == 'active' %}bg-green-100 text-green-800
                               {% elif record.status == 'follow_up' %}bg-yellow-100 text-yellow-800
                               {% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ record.get_status_display }}
                    </span>
                </div>
                
                <!-- Record Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="space-y-2">
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-calendar w-4 mr-2"></i>
                            <span class="font-medium">Date:</span>
                            <span class="ml-1">{{ record.date|date:"M d, Y" }}</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-user-md w-4 mr-2"></i>
                            <span class="font-medium">Doctor:</span>
                            <span class="ml-1">{{ record.doctor_name }}</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-hospital w-4 mr-2"></i>
                            <span class="font-medium">Facility:</span>
                            <span class="ml-1">{{ record.facility_name }}</span>
                        </div>
                    </div>
                    
                    <div class="space-y-2">
                        {% if record.symptoms %}
                        <div class="text-sm text-gray-600">
                            <span class="font-medium">Symptoms:</span>
                            <p class="mt-1">{{ record.symptoms|truncatechars:100 }}</p>
                        </div>
                        {% endif %}
                        
                        {% if record.treatment %}
                        <div class="text-sm text-gray-600">
                            <span class="font-medium">Treatment:</span>
                            <p class="mt-1">{{ record.treatment|truncatechars:100 }}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Action Button -->
                <div class="flex justify-end">
                    <a href="{% url 'patient_portal:medical_record_detail' record.id %}" 
                       class="inline-flex items-center px-4 py-2 border border-blue-300 rounded-md text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors">
                        <i class="fas fa-eye mr-2"></i>
                        View Details
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if medical_records.has_other_pages %}
<div class="mt-8 flex justify-center">
    <nav class="flex items-center space-x-2">
        {% if medical_records.has_previous %}
        <a href="?page={{ medical_records.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" 
           class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
            <i class="fas fa-chevron-left mr-1"></i>Previous
        </a>
        {% endif %}
        
        <span class="px-3 py-2 text-sm text-gray-700">
            Page {{ medical_records.number }} of {{ medical_records.paginator.num_pages }}
        </span>
        
        {% if medical_records.has_next %}
        <a href="?page={{ medical_records.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" 
           class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
            Next<i class="fas fa-chevron-right ml-1"></i>
        </a>
        {% endif %}
    </nav>
</div>
{% endif %}

        {% else %}
        <!-- Empty State -->
        <div class="content-card text-center py-12">
            <i class="fas fa-file-medical text-6xl text-gray-300 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Medical Records Found</h3>
            {% if search_query %}
            <p class="text-gray-500 mb-4">No records match your search for "{{ search_query }}"</p>
            <a href="{% url 'patient_portal:medical_records' %}"
               class="government-filter-button">
                <i class="fas fa-times mr-2"></i>Clear Search
            </a>
            {% else %}
            <p class="text-gray-500">You don't have any medical records in the system yet.</p>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
