{% extends 'patient_portal/base.html' %}
{% load static %}

{% block title %}Medical Record Details - ZimHealth-ID{% endblock %}

{% block content %}
<!-- Breadcrumb -->
<nav class="mb-6">
    <ol class="flex items-center space-x-2 text-sm text-gray-500">
        <li><a href="{% url 'patient_portal:dashboard' %}" class="hover:text-blue-600">Dashboard</a></li>
        <li><i class="fas fa-chevron-right"></i></li>
        <li><a href="{% url 'patient_portal:medical_records' %}" class="hover:text-blue-600">Medical Records</a></li>
        <li><i class="fas fa-chevron-right"></i></li>
        <li class="text-gray-900">Record Details</li>
    </ol>
</nav>

<!-- Page Header -->
<div class="patient-header">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold">Medical Record Details</h1>
            <p class="text-blue-100">{{ medical_record.date|date:"l, F d, Y" }}</p>
        </div>
        <div class="read-only-badge bg-white bg-opacity-20 text-white border-white">
            <i class="fas fa-eye mr-1"></i>
            Read-Only Access
        </div>
    </div>
</div>

<!-- Medical Record Details -->
<div class="patient-card">
    <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-semibold text-gray-900">{{ medical_record.diagnosis }}</h2>
        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                   {% if medical_record.status == 'active' %}bg-green-100 text-green-800
                   {% elif medical_record.status == 'follow_up' %}bg-yellow-100 text-yellow-800
                   {% else %}bg-gray-100 text-gray-800{% endif %}">
            {{ medical_record.get_status_display }}
        </span>
    </div>
    
    <!-- Visit Information -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">Visit Information</h3>
            
            <div class="space-y-3">
                <div class="flex items-center text-sm">
                    <i class="fas fa-calendar w-5 mr-3 text-gray-400"></i>
                    <span class="font-medium text-gray-700 w-20">Date:</span>
                    <span class="text-gray-900">{{ medical_record.date|date:"l, F d, Y" }}</span>
                </div>
                
                <div class="flex items-center text-sm">
                    <i class="fas fa-user-md w-5 mr-3 text-gray-400"></i>
                    <span class="font-medium text-gray-700 w-20">Doctor:</span>
                    <span class="text-gray-900">{{ medical_record.doctor_name }}</span>
                </div>
                
                <div class="flex items-center text-sm">
                    <i class="fas fa-hospital w-5 mr-3 text-gray-400"></i>
                    <span class="font-medium text-gray-700 w-20">Facility:</span>
                    <span class="text-gray-900">{{ medical_record.facility_name }}</span>
                </div>
                
                {% if medical_record.visit_type %}
                <div class="flex items-center text-sm">
                    <i class="fas fa-stethoscope w-5 mr-3 text-gray-400"></i>
                    <span class="font-medium text-gray-700 w-20">Type:</span>
                    <span class="text-gray-900">{{ medical_record.visit_type|title }}</span>
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">Patient Information</h3>
            
            <div class="space-y-3">
                <div class="flex items-center text-sm">
                    <i class="fas fa-id-card w-5 mr-3 text-gray-400"></i>
                    <span class="font-medium text-gray-700 w-20">ID:</span>
                    <span class="text-gray-900 font-mono">{{ patient.zimhealth_id }}</span>
                </div>
                
                <div class="flex items-center text-sm">
                    <i class="fas fa-user w-5 mr-3 text-gray-400"></i>
                    <span class="font-medium text-gray-700 w-20">Name:</span>
                    <span class="text-gray-900">{{ patient.full_name }}</span>
                </div>
                
                <div class="flex items-center text-sm">
                    <i class="fas fa-birthday-cake w-5 mr-3 text-gray-400"></i>
                    <span class="font-medium text-gray-700 w-20">Age:</span>
                    <span class="text-gray-900">{{ patient.age }} years</span>
                </div>
                
                {% if patient.blood_type %}
                <div class="flex items-center text-sm">
                    <i class="fas fa-tint w-5 mr-3 text-gray-400"></i>
                    <span class="font-medium text-gray-700 w-20">Blood:</span>
                    <span class="text-gray-900">{{ patient.blood_type }}</span>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Medical Details -->
    <div class="space-y-6">
        <!-- Symptoms -->
        {% if medical_record.symptoms %}
        <div>
            <h3 class="text-lg font-medium text-gray-900 mb-3">
                <i class="fas fa-thermometer-half text-red-500 mr-2"></i>
                Symptoms Reported
            </h3>
            <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                <p class="text-gray-800">{{ medical_record.symptoms }}</p>
            </div>
        </div>
        {% endif %}
        
        <!-- Diagnosis -->
        <div>
            <h3 class="text-lg font-medium text-gray-900 mb-3">
                <i class="fas fa-diagnoses text-blue-500 mr-2"></i>
                Diagnosis
            </h3>
            <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <p class="text-gray-800">{{ medical_record.diagnosis }}</p>
            </div>
        </div>
        
        <!-- Treatment -->
        {% if medical_record.treatment %}
        <div>
            <h3 class="text-lg font-medium text-gray-900 mb-3">
                <i class="fas fa-procedures text-green-500 mr-2"></i>
                Treatment Provided
            </h3>
            <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                <p class="text-gray-800">{{ medical_record.treatment }}</p>
            </div>
        </div>
        {% endif %}
        
        <!-- Notes -->
        {% if medical_record.notes %}
        <div>
            <h3 class="text-lg font-medium text-gray-900 mb-3">
                <i class="fas fa-sticky-note text-yellow-500 mr-2"></i>
                Additional Notes
            </h3>
            <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p class="text-gray-800">{{ medical_record.notes }}</p>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Related Prescriptions -->
{% if prescriptions %}
<div class="mt-8 patient-card">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">
        <i class="fas fa-pills text-blue-600 mr-2"></i>
        Prescriptions from this Visit
    </h2>
    
    <div class="space-y-3">
        {% for prescription in prescriptions %}
        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <h4 class="font-medium text-gray-900">{{ prescription.medication }}</h4>
                    <p class="text-sm text-gray-600">{{ prescription.dosage }} - {{ prescription.frequency }}</p>
                    <p class="text-sm text-gray-500">
                        <i class="fas fa-calendar mr-1"></i>{{ prescription.start_date|date:"M d, Y" }}
                        {% if prescription.end_date %} to {{ prescription.end_date|date:"M d, Y" }}{% endif %}
                    </p>
                </div>
                <div class="text-right">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                               {% if prescription.status == 'active' %}bg-green-100 text-green-800
                               {% elif prescription.status == 'completed' %}bg-gray-100 text-gray-800
                               {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                        {{ prescription.get_status_display }}
                    </span>
                    <div class="mt-2">
                        <a href="{% url 'patient_portal:prescription_detail' prescription.id %}" 
                           class="text-blue-600 hover:text-blue-800 text-sm">
                            View Details →
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}

<!-- Navigation -->
<div class="mt-8 flex justify-between">
    <a href="{% url 'patient_portal:medical_records' %}" 
       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
        <i class="fas fa-arrow-left mr-2"></i>
        Back to Medical Records
    </a>
    
    <a href="{% url 'patient_portal:dashboard' %}" 
       class="inline-flex items-center px-4 py-2 border border-blue-300 rounded-md text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors">
        <i class="fas fa-tachometer-alt mr-2"></i>
        Return to Dashboard
    </a>
</div>
{% endblock %}
