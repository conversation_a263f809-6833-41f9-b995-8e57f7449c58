{% extends 'patient_portal/base.html' %}
{% load static %}

{% block title %}My Prescriptions - ZimHealth-ID{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Professional Page Header -->
    <div class="dashboard-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="header-content">
                <!-- Left Section: Clean Professional Title -->
                <div class="header-left">
                    <h1 class="header-title">My Prescriptions</h1>
                    <p class="header-subtitle">Complete history of all medications prescribed to you</p>
                </div>

                <!-- Right Section: Status Badge -->
                <div class="header-right">
                    <div class="read-only-badge">
                        <i class="fas fa-eye mr-1"></i>
                        Read-Only Access
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Professional Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">

<!-- Statistics -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <div class="patient-stat-card">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-blue-600">Active</p>
                <p class="text-2xl font-bold text-blue-900">{{ active_count }}</p>
            </div>
            <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                <i class="fas fa-pills text-white"></i>
            </div>
        </div>
    </div>
    
    <div class="patient-stat-card">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-blue-600">Completed</p>
                <p class="text-2xl font-bold text-blue-900">{{ completed_count }}</p>
            </div>
            <div class="w-10 h-10 bg-gray-500 rounded-lg flex items-center justify-center">
                <i class="fas fa-check text-white"></i>
            </div>
        </div>
    </div>
    
    <div class="patient-stat-card">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-blue-600">Total</p>
                <p class="text-2xl font-bold text-blue-900">{{ total_count }}</p>
            </div>
            <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-list text-white"></i>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="patient-card mb-6">
    <form method="get" class="space-y-4">
        <div class="flex flex-col md:flex-row gap-4">
            <!-- Search -->
            <div class="flex-1">
                <div class="relative">
                    <input type="text" name="search" value="{{ search_query }}" 
                           class="government-search-input-compact w-full pl-10"
                           placeholder="Search by medication name, dosage, or instructions">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>
            
            <!-- Status Filter -->
            <div>
                <select name="status" class="government-select-compact">
                    <option value="">All Statuses</option>
                    <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Active</option>
                    <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>Completed</option>
                    <option value="discontinued" {% if status_filter == 'discontinued' %}selected{% endif %}>Discontinued</option>
                    <option value="on_hold" {% if status_filter == 'on_hold' %}selected{% endif %}>On Hold</option>
                </select>
            </div>
            
            <!-- Submit -->
            <button type="submit" class="government-filter-button">
                <i class="fas fa-filter mr-2"></i>Filter
            </button>
            
            {% if search_query or status_filter %}
            <a href="{% url 'patient_portal:prescriptions' %}" class="government-filter-button bg-gray-500 hover:bg-gray-600">
                <i class="fas fa-times mr-2"></i>Clear
            </a>
            {% endif %}
        </div>
    </form>
</div>

<!-- Prescriptions List -->
{% if prescriptions %}
<div class="space-y-4">
    {% for prescription in prescriptions %}
    <div class="patient-card hover:shadow-lg transition-shadow">
        <div class="flex items-start justify-between">
            <div class="flex-1">
                <!-- Prescription Header -->
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-lg font-semibold text-gray-900">{{ prescription.medication }}</h3>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                               {% if prescription.status == 'active' %}bg-green-100 text-green-800
                               {% elif prescription.status == 'completed' %}bg-gray-100 text-gray-800
                               {% elif prescription.status == 'discontinued' %}bg-red-100 text-red-800
                               {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                        {{ prescription.get_status_display }}
                    </span>
                </div>
                
                <!-- Prescription Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="space-y-2">
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-pills w-4 mr-2"></i>
                            <span class="font-medium">Dosage:</span>
                            <span class="ml-1">{{ prescription.dosage }}</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-clock w-4 mr-2"></i>
                            <span class="font-medium">Frequency:</span>
                            <span class="ml-1">{{ prescription.frequency }}</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-calendar w-4 mr-2"></i>
                            <span class="font-medium">Start Date:</span>
                            <span class="ml-1">{{ prescription.start_date|date:"M d, Y" }}</span>
                        </div>
                        {% if prescription.end_date %}
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-calendar-times w-4 mr-2"></i>
                            <span class="font-medium">End Date:</span>
                            <span class="ml-1">{{ prescription.end_date|date:"M d, Y" }}</span>
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="space-y-2">
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-user-md w-4 mr-2"></i>
                            <span class="font-medium">Prescribed by:</span>
                            <span class="ml-1">{{ prescription.medical_record.doctor_name }}</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-hospital w-4 mr-2"></i>
                            <span class="font-medium">Facility:</span>
                            <span class="ml-1">{{ prescription.medical_record.facility_name }}</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-file-medical w-4 mr-2"></i>
                            <span class="font-medium">Related to:</span>
                            <span class="ml-1">{{ prescription.medical_record.diagnosis|truncatechars:30 }}</span>
                        </div>
                    </div>
                </div>
                
                <!-- Instructions -->
                {% if prescription.instructions %}
                <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p class="text-sm font-medium text-blue-900 mb-1">
                        <i class="fas fa-info-circle mr-1"></i>Instructions
                    </p>
                    <p class="text-sm text-blue-800">{{ prescription.instructions }}</p>
                </div>
                {% endif %}
                
                <!-- Action Button -->
                <div class="flex justify-end">
                    <a href="{% url 'patient_portal:prescription_detail' prescription.id %}" 
                       class="inline-flex items-center px-4 py-2 border border-blue-300 rounded-md text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors">
                        <i class="fas fa-eye mr-2"></i>
                        View Details
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if prescriptions.has_other_pages %}
<div class="mt-8 flex justify-center">
    <nav class="flex items-center space-x-2">
        {% if prescriptions.has_previous %}
        <a href="?page={{ prescriptions.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
           class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
            <i class="fas fa-chevron-left mr-1"></i>Previous
        </a>
        {% endif %}
        
        <span class="px-3 py-2 text-sm text-gray-700">
            Page {{ prescriptions.number }} of {{ prescriptions.paginator.num_pages }}
        </span>
        
        {% if prescriptions.has_next %}
        <a href="?page={{ prescriptions.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
           class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
            Next<i class="fas fa-chevron-right ml-1"></i>
        </a>
        {% endif %}
    </nav>
</div>
{% endif %}

        {% else %}
        <!-- Empty State -->
        <div class="content-card text-center py-12">
            <i class="fas fa-pills text-6xl text-gray-300 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Prescriptions Found</h3>
            {% if search_query or status_filter %}
            <p class="text-gray-500 mb-4">No prescriptions match your current filters</p>
            <a href="{% url 'patient_portal:prescriptions' %}"
               class="government-filter-button">
                <i class="fas fa-times mr-2"></i>Clear Filters
            </a>
            {% else %}
            <p class="text-gray-500">You don't have any prescriptions in the system yet.</p>
            {% endif %}
        </div>
        {% endif %}

        <!-- Important Notice -->
        <div class="mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div class="flex items-start space-x-3">
                <i class="fas fa-exclamation-triangle text-yellow-600 mt-1"></i>
                <div>
                    <h3 class="text-sm font-medium text-yellow-800">Prescription Refills</h3>
                    <p class="text-sm text-yellow-700 mt-1">
                        To request prescription refills or discuss medication changes, please contact your healthcare provider directly.
                        This portal is for viewing your prescription history only.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
