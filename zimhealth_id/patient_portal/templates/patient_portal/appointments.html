{% extends 'patient_portal/base.html' %}
{% load static %}

{% block title %}My Appointments - ZimHealth-ID{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Professional Page Header -->
    <div class="dashboard-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="header-content">
                <!-- Left Section: Clean Professional Title -->
                <div class="header-left">
                    <h1 class="header-title">My Appointments</h1>
                    <p class="header-subtitle">View your upcoming and past appointments</p>
                </div>

                <!-- Right Section: Status Badge -->
                <div class="header-right">
                    <div class="read-only-badge">
                        <i class="fas fa-eye mr-1"></i>
                        Read-Only Access
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Professional Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">

<!-- Statistics -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <div class="patient-stat-card">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-blue-600">Upcoming</p>
                <p class="text-2xl font-bold text-blue-900">{{ upcoming_count }}</p>
            </div>
            <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                <i class="fas fa-calendar-plus text-white"></i>
            </div>
        </div>
    </div>
    
    <div class="patient-stat-card">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-blue-600">Completed</p>
                <p class="text-2xl font-bold text-blue-900">{{ completed_count }}</p>
            </div>
            <div class="w-10 h-10 bg-gray-500 rounded-lg flex items-center justify-center">
                <i class="fas fa-check text-white"></i>
            </div>
        </div>
    </div>
    
    <div class="patient-stat-card">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-blue-600">Total</p>
                <p class="text-2xl font-bold text-blue-900">{{ total_count }}</p>
            </div>
            <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-calendar text-white"></i>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="patient-card mb-6">
    <form method="get" class="space-y-4">
        <div class="flex flex-col md:flex-row gap-4">
            <!-- Status Filter -->
            <div>
                <select name="status" class="government-select-compact">
                    <option value="">All Statuses</option>
                    <option value="scheduled" {% if status_filter == 'scheduled' %}selected{% endif %}>Scheduled</option>
                    <option value="confirmed" {% if status_filter == 'confirmed' %}selected{% endif %}>Confirmed</option>
                    <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>Completed</option>
                    <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>Cancelled</option>
                    <option value="no_show" {% if status_filter == 'no_show' %}selected{% endif %}>No Show</option>
                </select>
            </div>
            
            <!-- Date Range Filter -->
            <div>
                <select name="date_range" class="government-select-compact">
                    <option value="">All Dates</option>
                    <option value="upcoming" {% if date_filter == 'upcoming' %}selected{% endif %}>Upcoming</option>
                    <option value="past" {% if date_filter == 'past' %}selected{% endif %}>Past</option>
                    <option value="this_month" {% if date_filter == 'this_month' %}selected{% endif %}>This Month</option>
                </select>
            </div>
            
            <!-- Submit -->
            <button type="submit" class="government-filter-button">
                <i class="fas fa-filter mr-2"></i>Filter
            </button>
            
            {% if status_filter or date_filter %}
            <a href="{% url 'patient_portal:appointments' %}" class="government-filter-button bg-gray-500 hover:bg-gray-600">
                <i class="fas fa-times mr-2"></i>Clear
            </a>
            {% endif %}
        </div>
    </form>
</div>

<!-- Appointments List -->
{% if appointments %}
<div class="space-y-4">
    {% for appointment in appointments %}
    <div class="patient-card hover:shadow-lg transition-shadow">
        <div class="flex items-start justify-between">
            <div class="flex-1">
                <!-- Appointment Header -->
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-lg font-semibold text-gray-900">{{ appointment.appointment_type|title }}</h3>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                               {% if appointment.status == 'scheduled' %}bg-blue-100 text-blue-800
                               {% elif appointment.status == 'confirmed' %}bg-green-100 text-green-800
                               {% elif appointment.status == 'completed' %}bg-gray-100 text-gray-800
                               {% elif appointment.status == 'cancelled' %}bg-red-100 text-red-800
                               {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                        {{ appointment.get_status_display }}
                    </span>
                </div>
                
                <!-- Appointment Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="space-y-2">
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-calendar w-4 mr-2"></i>
                            <span class="font-medium">Date:</span>
                            <span class="ml-1">{{ appointment.date|date:"l, M d, Y" }}</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-clock w-4 mr-2"></i>
                            <span class="font-medium">Time:</span>
                            <span class="ml-1">{{ appointment.time|time:"g:i A" }}</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-user-md w-4 mr-2"></i>
                            <span class="font-medium">Doctor:</span>
                            <span class="ml-1">{{ appointment.doctor_name }}</span>
                        </div>
                    </div>
                    
                    <div class="space-y-2">
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-hospital w-4 mr-2"></i>
                            <span class="font-medium">Facility:</span>
                            <span class="ml-1">{{ appointment.facility_name }}</span>
                        </div>
                        {% if appointment.duration %}
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-hourglass-half w-4 mr-2"></i>
                            <span class="font-medium">Duration:</span>
                            <span class="ml-1">{{ appointment.duration }}</span>
                        </div>
                        {% endif %}
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-plus w-4 mr-2"></i>
                            <span class="font-medium">Created:</span>
                            <span class="ml-1">{{ appointment.created_at|date:"M d, Y" }}</span>
                        </div>
                    </div>
                </div>
                
                <!-- Notes -->
                {% if appointment.notes %}
                <div class="mb-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                    <p class="text-sm font-medium text-gray-900 mb-1">
                        <i class="fas fa-sticky-note mr-1"></i>Notes
                    </p>
                    <p class="text-sm text-gray-700">{{ appointment.notes }}</p>
                </div>
                {% endif %}
                
                <!-- Action Button -->
                <div class="flex justify-end">
                    <a href="{% url 'patient_portal:appointment_detail' appointment.id %}" 
                       class="inline-flex items-center px-4 py-2 border border-blue-300 rounded-md text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors">
                        <i class="fas fa-eye mr-2"></i>
                        View Details
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if appointments.has_other_pages %}
<div class="mt-8 flex justify-center">
    <nav class="flex items-center space-x-2">
        {% if appointments.has_previous %}
        <a href="?page={{ appointments.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if date_filter %}&date_range={{ date_filter }}{% endif %}" 
           class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
            <i class="fas fa-chevron-left mr-1"></i>Previous
        </a>
        {% endif %}
        
        <span class="px-3 py-2 text-sm text-gray-700">
            Page {{ appointments.number }} of {{ appointments.paginator.num_pages }}
        </span>
        
        {% if appointments.has_next %}
        <a href="?page={{ appointments.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if date_filter %}&date_range={{ date_filter }}{% endif %}" 
           class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
            Next<i class="fas fa-chevron-right ml-1"></i>
        </a>
        {% endif %}
    </nav>
</div>
{% endif %}

        {% else %}
        <!-- Empty State -->
        <div class="content-card text-center py-12">
            <i class="fas fa-calendar-check text-6xl text-gray-300 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Appointments Found</h3>
            {% if status_filter or date_filter %}
            <p class="text-gray-500 mb-4">No appointments match your current filters</p>
            <a href="{% url 'patient_portal:appointments' %}"
               class="government-filter-button">
                <i class="fas fa-times mr-2"></i>Clear Filters
            </a>
            {% else %}
            <p class="text-gray-500">You don't have any appointments scheduled yet.</p>
            {% endif %}
        </div>
        {% endif %}

        <!-- Scheduling Notice -->
        <div class="mt-8 p-6 bg-medical-50 border border-medical-200 rounded-lg">
            <div class="flex items-start space-x-3">
                <i class="fas fa-info-circle text-medical-600 mt-1"></i>
                <div>
                    <h3 class="text-sm font-medium text-medical-800">Schedule New Appointments</h3>
                    <p class="text-sm text-medical-700 mt-1">
                        To schedule new appointments or modify existing ones, please contact your healthcare provider directly.
                        This portal shows your appointment history and upcoming scheduled visits.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
