<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ZimHealth-ID Patient Portal{% endblock %}</title>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        'medical': {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        'health': {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        }
                    }
                }
            }
        }
    </script>

    <!-- Custom CSS - Load AFTER Tailwind to ensure proper override -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}">
    <link rel="stylesheet" href="{% static 'assets/css/patients.css' %}">
    <link rel="stylesheet" href="{% static 'assets/css/patient_success_modal.css' %}">

    <!-- Professional Patient Portal Styling -->
    <style>
        .auth-form-container {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95)) !important;
            backdrop-filter: blur(25px) !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
            box-shadow: 0 20px 40px -12px rgba(14, 165, 233, 0.15), 0 8px 25px -8px rgba(0, 0, 0, 0.1) !important;
            border-radius: 16px !important;
            padding: 2rem !important;
            margin: 1rem 0 !important;
        }

        .auth-input {
            width: 100% !important;
            padding: 0.75rem 1rem 0.75rem 2.5rem !important;
            border: 1px solid #d1d5db !important;
            border-radius: 0.5rem !important;
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95)) !important;
            backdrop-filter: blur(10px) !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
        }

        .auth-input:focus {
            outline: none !important;
            border-color: #0284c7 !important;
            box-shadow: 0 0 0 3px rgba(2, 132, 199, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1) !important;
            background: rgba(255, 255, 255, 1) !important;
        }

        .government-filter-button {
            display: inline-flex !important;
            align-items: center !important;
            padding: 0.5rem 1rem !important;
            border: 1px solid transparent !important;
            font-size: 0.875rem !important;
            font-weight: 500 !important;
            border-radius: 0.5rem !important;
            color: white !important;
            background: linear-gradient(135deg, #0284c7, #0369a1) !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
            transition: all 0.2s ease !important;
            cursor: pointer !important;
            text-decoration: none !important;
        }

        .government-filter-button:hover {
            background: linear-gradient(135deg, #0369a1, #075985) !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-50 font-sans antialiased min-h-screen flex flex-col">
    <!-- Enhanced Navigation -->
    <nav class="main-navigation sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-18 py-2">
                <!-- Subtle Professional Logo -->
                <div class="flex items-center">
                    <a href="{% url 'patient_portal:dashboard' %}"
                       class="flex items-center space-x-3">
                        <div class="nav-logo">
                            <i class="fas fa-user-shield text-sm"></i>
                        </div>
                        <div>
                            <span class="text-xl font-bold text-gray-900">ZimHealth-ID</span>
                            <div class="text-xs text-gray-500 font-medium">Patient Portal</div>
                        </div>
                    </a>
                </div>
                
                <!-- Enhanced Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <!-- Navigation Buttons with Proper Spacing -->
                    <div class="flex items-center space-x-1">
                    {% if user.is_authenticated %}
                        <!-- Professional Navigation Links -->
                        <a href="{% url 'patient_portal:dashboard' %}" class="nav-link text-gray-700 hover:text-medical-600 px-3 py-2 font-medium flex items-center space-x-2 {% if request.resolver_match.url_name == 'dashboard' %}text-medical-600 bg-medical-50{% endif %}">
                            <i class="fas fa-tachometer-alt text-sm"></i>
                            <span>Dashboard</span>
                        </a>
                        <a href="{% url 'patient_portal:medical_records' %}" class="nav-link text-gray-700 hover:text-medical-600 px-3 py-2 font-medium flex items-center space-x-2 {% if 'medical_record' in request.resolver_match.url_name %}text-medical-600 bg-medical-50{% endif %}">
                            <i class="fas fa-file-medical text-sm"></i>
                            <span>Records</span>
                        </a>
                        <a href="{% url 'patient_portal:prescriptions' %}" class="nav-link text-gray-700 hover:text-medical-600 px-3 py-2 font-medium flex items-center space-x-2 {% if 'prescription' in request.resolver_match.url_name %}text-medical-600 bg-medical-50{% endif %}">
                            <i class="fas fa-pills text-sm"></i>
                            <span>Prescriptions</span>
                        </a>
                        <a href="{% url 'patient_portal:appointments' %}" class="nav-link text-gray-700 hover:text-medical-600 px-3 py-2 font-medium flex items-center space-x-2 {% if 'appointment' in request.resolver_match.url_name %}text-medical-600 bg-medical-50{% endif %}">
                            <i class="fas fa-calendar text-sm"></i>
                            <span>Appointments</span>
                        </a>
                        <a href="{% url 'patient_portal:doctors' %}" class="nav-link text-gray-700 hover:text-medical-600 px-3 py-2 font-medium flex items-center space-x-2 {% if request.resolver_match.url_name == 'doctors' %}text-medical-600 bg-medical-50{% endif %}">
                            <i class="fas fa-user-md text-sm"></i>
                            <span>My Doctors</span>
                        </a>
                    {% endif %}
                    </div>

                    <!-- Professional User Section -->
                    <div class="flex items-center space-x-6">
                        {% if user.is_authenticated %}
                        <!-- User Information -->
                        <div class="hidden lg:flex items-center space-x-3">
                            <div class="text-right">
                                <p class="text-sm font-medium text-gray-900">{{ user.first_name }} {{ user.last_name }}</p>
                                <p class="text-xs text-gray-500">ID: {{ user.patient_portal_profile.patient.zimhealth_id }}</p>
                            </div>
                            <div class="w-10 h-10 bg-medical-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-medical-600"></i>
                            </div>
                        </div>

                        <!-- User Actions -->
                        <div class="flex items-center space-x-2">
                            <a href="{% url 'patient_portal:profile' %}"
                               class="nav-link text-gray-700 hover:text-medical-600 px-3 py-2 font-medium flex items-center space-x-2 {% if request.resolver_match.url_name == 'profile' %}text-medical-600 bg-medical-50{% endif %}">
                                <i class="fas fa-user-cog text-sm"></i>
                                <span class="hidden lg:inline">Profile</span>
                            </a>
                            <a href="{% url 'patient_portal:logout' %}" class="nav-link text-gray-700 hover:text-red-600 px-3 py-2 font-medium flex items-center space-x-2">
                                <i class="fas fa-sign-out-alt text-sm"></i>
                                <span class="hidden lg:inline">Logout</span>
                            </a>
                        </div>
                        {% else %}
                        <div class="flex items-center space-x-4">
                            <a href="{% url 'patient_portal:login' %}" class="nav-link text-gray-700 hover:text-medical-600 px-3 py-2 font-medium flex items-center space-x-2">
                                <i class="fas fa-sign-in-alt text-sm"></i>
                                <span>Login</span>
                            </a>
                            <a href="{% url 'patient_portal:register' %}" class="nav-link text-gray-700 hover:text-medical-600 px-3 py-2 font-medium flex items-center space-x-2">
                                <i class="fas fa-user-plus text-sm"></i>
                                <span>Register</span>
                            </a>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Mobile menu button -->
                    <div class="md:hidden">
                        <button id="mobile-menu-button" class="text-gray-700 hover:text-medical-600 p-2">
                            <i class="fas fa-bars text-lg"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t border-gray-200">
            <div class="px-4 py-2 space-y-2">
                {% if user.is_authenticated %}
                    <div class="flex items-center space-x-3 py-3 border-b border-gray-200">
                        <div class="w-10 h-10 bg-medical-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-medical-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">{{ user.first_name|default:user.username }}</p>
                            <p class="text-sm text-gray-500">{{ user.patient_portal_profile.patient.zimhealth_id }}</p>
                        </div>
                    </div>
                    <a href="{% url 'patient_portal:dashboard' %}"
                       class="flex items-center space-x-3 py-2 text-gray-700 hover:text-medical-600 {% if request.resolver_match.url_name == 'dashboard' %}text-medical-600 bg-medical-50{% endif %}">
                        <i class="fas fa-tachometer-alt text-medical-600 w-5"></i>
                        <span>Dashboard</span>
                    </a>
                    <a href="{% url 'patient_portal:medical_records' %}"
                       class="flex items-center space-x-3 py-2 text-gray-700 hover:text-medical-600 {% if 'medical_record' in request.resolver_match.url_name %}text-medical-600 bg-medical-50{% endif %}">
                        <i class="fas fa-file-medical text-medical-600 w-5"></i>
                        <span>Medical Records</span>
                    </a>
                    <a href="{% url 'patient_portal:prescriptions' %}"
                       class="flex items-center space-x-3 py-2 text-gray-700 hover:text-medical-600 {% if 'prescription' in request.resolver_match.url_name %}text-medical-600 bg-medical-50{% endif %}">
                        <i class="fas fa-pills text-medical-600 w-5"></i>
                        <span>Prescriptions</span>
                    </a>
                    <a href="{% url 'patient_portal:appointments' %}"
                       class="flex items-center space-x-3 py-2 text-gray-700 hover:text-medical-600 {% if 'appointment' in request.resolver_match.url_name %}text-medical-600 bg-medical-50{% endif %}">
                        <i class="fas fa-calendar-check text-medical-600 w-5"></i>
                        <span>Appointments</span>
                    </a>
                    <a href="{% url 'patient_portal:doctors' %}"
                       class="flex items-center space-x-3 py-2 text-gray-700 hover:text-medical-600 {% if request.resolver_match.url_name == 'doctors' %}text-medical-600 bg-medical-50{% endif %}">
                        <i class="fas fa-user-md text-medical-600 w-5"></i>
                        <span>My Doctors</span>
                    </a>
                    <a href="{% url 'patient_portal:profile' %}"
                       class="flex items-center space-x-3 py-2 text-gray-700 hover:text-medical-600 {% if request.resolver_match.url_name == 'profile' %}text-medical-600 bg-medical-50{% endif %}">
                        <i class="fas fa-user-cog text-medical-600 w-5"></i>
                        <span>Profile</span>
                    </a>
                    <a href="{% url 'patient_portal:logout' %}"
                       class="flex items-center space-x-3 py-2 text-gray-700 hover:text-red-600">
                        <i class="fas fa-sign-out-alt text-red-600 w-5"></i>
                        <span>Logout</span>
                    </a>
                {% else %}
                    <a href="{% url 'patient_portal:login' %}"
                       class="flex items-center space-x-3 py-2 text-gray-700 hover:text-medical-600">
                        <i class="fas fa-sign-in-alt text-medical-600 w-5"></i>
                        <span>Login</span>
                    </a>
                    <a href="{% url 'patient_portal:register' %}"
                       class="flex items-center space-x-3 py-2 text-gray-700 hover:text-medical-600">
                        <i class="fas fa-user-plus text-medical-600 w-5"></i>
                        <span>Register</span>
                    </a>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="flex-1">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <!-- Messages (following provider dashboard pattern) -->
            {% if messages %}
            <div class="mb-6">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} mb-4 p-4 rounded-lg border-l-4
                            {% if message.tags == 'success' %}bg-health-50 border-health-400 text-health-700
                            {% elif message.tags == 'error' %}bg-red-50 border-red-400 text-red-700
                            {% elif message.tags == 'warning' %}bg-yellow-50 border-yellow-400 text-yellow-700
                            {% else %}bg-medical-50 border-medical-400 text-medical-700{% endif %}">
                    <div class="flex items-center">
                        <i class="fas {% if message.tags == 'success' %}fa-check-circle
                                  {% elif message.tags == 'error' %}fa-exclamation-circle
                                  {% elif message.tags == 'warning' %}fa-exclamation-triangle
                                  {% else %}fa-info-circle{% endif %} mr-2"></i>
                        {{ message }}
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- Page Content -->
            {% block content %}
            {% endblock %}
        </div>
    </main>

    <!-- Professional Footer -->
    <footer class="bg-gray-900 text-white mt-auto">
        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <p class="text-gray-300">
                    <i class="fas fa-shield-alt mr-2"></i>
                    ZimHealth-ID Patient Portal - Secure Access to Your Medical Information
                </p>
                <p class="text-gray-400 text-sm mt-2">
                    Your health data is protected and secure. Only you can access your medical information.
                </p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-button')?.addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Enhanced notification system (matching provider dashboard)
        function removeNotification(notification) {
            notification.style.transform = 'translateX(100%) scale(0.9)';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 500);
        }

        // Auto-remove notifications after 5 seconds
        document.querySelectorAll('.alert').forEach(alert => {
            setTimeout(() => {
                removeNotification(alert);
            }, 5000);
        });

        // Global notification system for patient portal
        function showNotification(message, type = 'info') {
            const existing = document.querySelector('.patient-notification');
            if (existing) existing.remove();

            const notification = document.createElement('div');
            const typeConfig = {
                success: { bg: 'bg-health-500', icon: 'fa-check-circle' },
                error: { bg: 'bg-red-500', icon: 'fa-exclamation-circle' },
                warning: { bg: 'bg-orange-500', icon: 'fa-exclamation-triangle' },
                info: { bg: 'bg-medical-500', icon: 'fa-info-circle' }
            };

            const config = typeConfig[type] || typeConfig.info;

            notification.className = `patient-notification fixed top-20 right-4 z-50 p-4 rounded-xl shadow-2xl max-w-sm transform translate-x-full transition-all duration-500 ${config.bg} text-white backdrop-blur-lg`;

            notification.innerHTML = `
                <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                        <i class="fas ${config.icon} text-lg"></i>
                    </div>
                    <div class="flex-1">
                        <p class="font-semibold text-sm">${message}</p>
                        <p class="text-xs opacity-90 mt-1">Patient Portal</p>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-2 hover:bg-white hover:bg-opacity-20 rounded-full p-1 transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => notification.remove(), 500);
            }, 5000);
        }
    </script>

    <!-- Mobile Navigation JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');

            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');

                    // Update button icon
                    const icon = mobileMenuButton.querySelector('i');
                    if (mobileMenu.classList.contains('hidden')) {
                        icon.className = 'fas fa-bars text-lg';
                    } else {
                        icon.className = 'fas fa-times text-lg';
                    }
                });
            }
        });
    </script>

    <!-- Modal JavaScript -->
    <script src="{% static 'assets/js/patient_success_modal.js' %}"></script>

    {% block extra_js %}
    {% endblock %}
</body>
</html>
