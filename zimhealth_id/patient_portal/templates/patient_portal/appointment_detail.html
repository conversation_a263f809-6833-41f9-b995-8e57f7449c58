{% extends 'patient_portal/base.html' %}
{% load static %}

{% block title %}Appointment Details - ZimHealth-ID{% endblock %}

{% block content %}
<!-- Breadcrumb -->
<nav class="mb-6">
    <ol class="flex items-center space-x-2 text-sm text-gray-500">
        <li><a href="{% url 'patient_portal:dashboard' %}" class="hover:text-blue-600">Dashboard</a></li>
        <li><i class="fas fa-chevron-right"></i></li>
        <li><a href="{% url 'patient_portal:appointments' %}" class="hover:text-blue-600">Appointments</a></li>
        <li><i class="fas fa-chevron-right"></i></li>
        <li class="text-gray-900">Appointment Details</li>
    </ol>
</nav>

<!-- Page Header -->
<div class="patient-header">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold">{{ appointment.appointment_type|title }}</h1>
            <p class="text-blue-100">{{ appointment.date|date:"l, F d, Y" }} at {{ appointment.time|time:"g:i A" }}</p>
        </div>
        <div class="text-right">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white bg-opacity-20 text-white border border-white border-opacity-30
                       {% if appointment.status == 'scheduled' %}bg-blue-100 text-blue-800 border-blue-200
                       {% elif appointment.status == 'confirmed' %}bg-green-100 text-green-800 border-green-200
                       {% elif appointment.status == 'completed' %}bg-gray-100 text-gray-800 border-gray-200
                       {% elif appointment.status == 'cancelled' %}bg-red-100 text-red-800 border-red-200
                       {% else %}bg-yellow-100 text-yellow-800 border-yellow-200{% endif %}">
                {{ appointment.get_status_display }}
            </span>
            <div class="read-only-badge bg-white bg-opacity-20 text-white border-white mt-2">
                <i class="fas fa-eye mr-1"></i>
                Read-Only Access
            </div>
        </div>
    </div>
</div>

<!-- Appointment Information -->
<div class="patient-card">
    <h2 class="text-lg font-semibold text-gray-900 mb-6">
        <i class="fas fa-calendar-check text-blue-600 mr-2"></i>
        Appointment Information
    </h2>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Appointment Details -->
        <div class="space-y-4">
            <h3 class="text-md font-medium text-gray-900 border-b border-gray-200 pb-2">Appointment Details</h3>
            
            <div class="space-y-3">
                <div class="flex items-start">
                    <i class="fas fa-calendar w-5 mr-3 text-gray-400 mt-1"></i>
                    <div>
                        <span class="font-medium text-gray-700">Date:</span>
                        <p class="text-gray-900">{{ appointment.date|date:"l, F d, Y" }}</p>
                    </div>
                </div>
                
                <div class="flex items-start">
                    <i class="fas fa-clock w-5 mr-3 text-gray-400 mt-1"></i>
                    <div>
                        <span class="font-medium text-gray-700">Time:</span>
                        <p class="text-gray-900">{{ appointment.time|time:"g:i A" }}</p>
                    </div>
                </div>
                
                <div class="flex items-start">
                    <i class="fas fa-stethoscope w-5 mr-3 text-gray-400 mt-1"></i>
                    <div>
                        <span class="font-medium text-gray-700">Type:</span>
                        <p class="text-gray-900">{{ appointment.appointment_type|title }}</p>
                    </div>
                </div>
                
                {% if appointment.duration %}
                <div class="flex items-start">
                    <i class="fas fa-hourglass-half w-5 mr-3 text-gray-400 mt-1"></i>
                    <div>
                        <span class="font-medium text-gray-700">Duration:</span>
                        <p class="text-gray-900">{{ appointment.duration }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Provider Information -->
        <div class="space-y-4">
            <h3 class="text-md font-medium text-gray-900 border-b border-gray-200 pb-2">Healthcare Provider</h3>
            
            <div class="space-y-3">
                <div class="flex items-start">
                    <i class="fas fa-user-md w-5 mr-3 text-gray-400 mt-1"></i>
                    <div>
                        <span class="font-medium text-gray-700">Doctor:</span>
                        <p class="text-gray-900">{{ appointment.doctor_name }}</p>
                    </div>
                </div>
                
                <div class="flex items-start">
                    <i class="fas fa-hospital w-5 mr-3 text-gray-400 mt-1"></i>
                    <div>
                        <span class="font-medium text-gray-700">Facility:</span>
                        <p class="text-gray-900">{{ appointment.facility_name }}</p>
                    </div>
                </div>
                
                {% if appointment.department %}
                <div class="flex items-start">
                    <i class="fas fa-building w-5 mr-3 text-gray-400 mt-1"></i>
                    <div>
                        <span class="font-medium text-gray-700">Department:</span>
                        <p class="text-gray-900">{{ appointment.department }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Patient Information -->
<div class="mt-6 patient-card">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">
        <i class="fas fa-user text-blue-600 mr-2"></i>
        Patient Information
    </h2>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="space-y-2">
            <p class="text-sm font-medium text-gray-500">ZimHealth-ID</p>
            <p class="text-gray-900 font-mono">{{ patient.zimhealth_id }}</p>
        </div>
        
        <div class="space-y-2">
            <p class="text-sm font-medium text-gray-500">Full Name</p>
            <p class="text-gray-900">{{ patient.full_name }}</p>
        </div>
        
        <div class="space-y-2">
            <p class="text-sm font-medium text-gray-500">Age at Appointment</p>
            <p class="text-gray-900">{{ patient.age }} years</p>
        </div>
    </div>
</div>

<!-- Notes -->
{% if appointment.notes %}
<div class="mt-6 patient-card">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">
        <i class="fas fa-sticky-note text-blue-600 mr-2"></i>
        Appointment Notes
    </h2>
    <div class="p-4 bg-gray-50 border border-gray-200 rounded-lg">
        <p class="text-gray-800 leading-relaxed">{{ appointment.notes }}</p>
    </div>
</div>
{% endif %}

<!-- Appointment History -->
<div class="mt-6 patient-card">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">
        <i class="fas fa-history text-blue-600 mr-2"></i>
        Appointment Timeline
    </h2>
    
    <div class="space-y-3">
        <div class="flex items-center text-sm">
            <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
            <span class="font-medium text-gray-700">Created:</span>
            <span class="ml-2 text-gray-900">{{ appointment.created_at|date:"F d, Y g:i A" }}</span>
        </div>
        
        {% if appointment.updated_at != appointment.created_at %}
        <div class="flex items-center text-sm">
            <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
            <span class="font-medium text-gray-700">Last Updated:</span>
            <span class="ml-2 text-gray-900">{{ appointment.updated_at|date:"F d, Y g:i A" }}</span>
        </div>
        {% endif %}
        
        {% if appointment.status == 'completed' %}
        <div class="flex items-center text-sm">
            <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
            <span class="font-medium text-gray-700">Completed:</span>
            <span class="ml-2 text-gray-900">{{ appointment.date|date:"F d, Y" }}</span>
        </div>
        {% elif appointment.status == 'cancelled' %}
        <div class="flex items-center text-sm">
            <div class="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
            <span class="font-medium text-gray-700">Cancelled:</span>
            <span class="ml-2 text-gray-900">{{ appointment.updated_at|date:"F d, Y" }}</span>
        </div>
        {% endif %}
    </div>
</div>

<!-- Important Notice -->
<div class="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
    <div class="flex items-start space-x-3">
        <i class="fas fa-info-circle text-blue-600 mt-1"></i>
        <div>
            <h3 class="text-sm font-medium text-blue-800">Appointment Changes</h3>
            <p class="text-sm text-blue-700 mt-1">
                To reschedule, cancel, or modify this appointment, please contact your healthcare provider directly. 
                Changes cannot be made through this patient portal.
            </p>
        </div>
    </div>
</div>

<!-- Navigation -->
<div class="mt-8 flex justify-between">
    <a href="{% url 'patient_portal:appointments' %}" 
       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
        <i class="fas fa-arrow-left mr-2"></i>
        Back to Appointments
    </a>
    
    <a href="{% url 'patient_portal:dashboard' %}" 
       class="inline-flex items-center px-4 py-2 border border-blue-300 rounded-md text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors">
        <i class="fas fa-tachometer-alt mr-2"></i>
        Return to Dashboard
    </a>
</div>
{% endblock %}
