{% extends 'patient_portal/base.html' %}
{% load static %}

{% block title %}Patient Registration - ZimHealth-ID{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-lg w-full space-y-8">
        <!-- Professional Header -->
        <div class="text-center">
            <div class="mx-auto h-16 w-16 bg-medical-600 rounded-full flex items-center justify-center mb-4">
                <i class="fas fa-user-plus text-white text-2xl"></i>
            </div>
            <h2 class="text-3xl font-bold text-gray-900 mb-2">Create Patient Account</h2>
            <p class="text-gray-600">Register to access your medical information</p>
            <div class="read-only-badge mt-3">
                <i class="fas fa-eye mr-1"></i>
                Read-Only Access Portal
            </div>
        </div>

        <!-- Professional Registration Form -->
        <div class="auth-form-container">
            <form method="post" class="space-y-6">
                {% csrf_token %}
            
            <div class="space-y-4">
                <!-- ZimHealth-ID Field -->
                <div>
                    <label for="{{ form.zimhealth_id.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        ZimHealth-ID *
                    </label>
                    <div class="flex gap-3">
                        <div class="relative flex-1">
                            <input type="text" name="{{ form.zimhealth_id.name }}" id="{{ form.zimhealth_id.id_for_label }}"
                                   class="auth-input pl-10" placeholder="Enter your ZimHealth-ID"
                                   value="{{ form.zimhealth_id.value|default:'' }}" required>
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                <i class="fas fa-id-card text-gray-400"></i>
                            </div>
                        </div>
                        <button type="button" id="qr-scanner-btn" class="government-filter-button flex items-center space-x-2">
                            <i class="fas fa-qrcode"></i>
                            <span>Scan QR</span>
                        </button>
                    </div>
                    {% if form.zimhealth_id.help_text %}
                    <p class="mt-1 text-sm text-gray-500">{{ form.zimhealth_id.help_text }}</p>
                    {% endif %}
                    {% if form.zimhealth_id.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.zimhealth_id.errors %}
                        <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <!-- Name Fields -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            First Name *
                        </label>
                        <div class="relative">
                            <input type="text" name="{{ form.first_name.name }}" id="{{ form.first_name.id_for_label }}"
                                   class="auth-input pl-10" placeholder="Enter your first name"
                                   value="{{ form.first_name.value|default:'' }}" required>
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                <i class="fas fa-user text-gray-400"></i>
                            </div>
                        </div>
                        {% if form.first_name.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.first_name.errors %}
                            <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Last Name *
                        </label>
                        <div class="relative">
                            <input type="text" name="{{ form.last_name.name }}" id="{{ form.last_name.id_for_label }}"
                                   class="auth-input pl-10" placeholder="Enter your last name"
                                   value="{{ form.last_name.value|default:'' }}" required>
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                <i class="fas fa-user text-gray-400"></i>
                            </div>
                        </div>
                        {% if form.last_name.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.last_name.errors %}
                            <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Contact Information -->
                <div>
                    <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Email Address *
                    </label>
                    <div class="relative">
                        <input type="email" name="{{ form.email.name }}" id="{{ form.email.id_for_label }}"
                               class="auth-input pl-10" placeholder="Enter your email address"
                               value="{{ form.email.value|default:'' }}" required>
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                            <i class="fas fa-envelope text-gray-400"></i>
                        </div>
                    </div>
                    {% if form.email.help_text %}
                    <p class="mt-1 text-sm text-gray-500">{{ form.email.help_text }}</p>
                    {% endif %}
                    {% if form.email.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.email.errors %}
                        <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.phone_number.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number *
                    </label>
                    <div class="relative">
                        <input type="tel" name="{{ form.phone_number.name }}" id="{{ form.phone_number.id_for_label }}"
                               class="auth-input pl-10" placeholder="Enter your phone number"
                               value="{{ form.phone_number.value|default:'' }}" required>
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                            <i class="fas fa-phone text-gray-400"></i>
                        </div>
                    </div>
                    {% if form.phone_number.help_text %}
                    <p class="mt-1 text-sm text-gray-500">{{ form.phone_number.help_text }}</p>
                    {% endif %}
                    {% if form.phone_number.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.phone_number.errors %}
                        <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <!-- Password Fields -->
                <div>
                    <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Create Password *
                    </label>
                    <div class="relative">
                        <input type="password" name="{{ form.password1.name }}" id="{{ form.password1.id_for_label }}"
                               class="auth-input pl-10" placeholder="Create a secure password" required>
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                    </div>
                    {% if form.password1.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.password1.errors %}
                        <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Confirm Password *
                    </label>
                    <div class="relative">
                        <input type="password" name="{{ form.password2.name }}" id="{{ form.password2.id_for_label }}"
                               class="auth-input pl-10" placeholder="Confirm your password" required>
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                    </div>
                    {% if form.password2.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.password2.errors %}
                        <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <!-- Terms and Conditions -->
                <div class="flex items-start space-x-3">
                    <input type="checkbox" name="{{ form.terms_accepted.name }}" id="{{ form.terms_accepted.id_for_label }}"
                           class="auth-checkbox mt-1" required>
                    <label for="{{ form.terms_accepted.id_for_label }}" class="text-sm text-gray-700">
                        {{ form.terms_accepted.label }}
                    </label>
                </div>
                {% if form.terms_accepted.errors %}
                <div class="text-sm text-red-600">
                    {% for error in form.terms_accepted.errors %}
                    <p>{{ error }}</p>
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <!-- Submit Button -->
            <div>
                <button type="submit" class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-medical-600 hover:bg-medical-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-medical-500 transition-colors duration-200">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <i class="fas fa-user-plus text-medical-500 group-hover:text-medical-400"></i>
                    </span>
                    Create My Patient Account
                </button>
            </div>

            <!-- Links -->
            <div class="text-center space-y-2">
                <p class="text-sm text-gray-600">
                    Already have an account?
                    <a href="{% url 'patient_portal:login' %}" class="font-medium text-medical-600 hover:text-medical-500">
                        Login here
                    </a>
                </p>
                <p class="text-sm text-gray-500">
                    Healthcare Provider?
                    <a href="{% url 'zhid_auth:login' %}" class="font-medium text-gray-700 hover:text-gray-600">
                        Provider Login
                    </a>
                </p>
            </div>
            </form>
        </div>

        <!-- Information Notice -->
        <div class="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div class="flex items-start space-x-3">
                <i class="fas fa-info-circle text-blue-600 mt-1"></i>
                <div>
                    <h4 class="text-sm font-medium text-blue-900">Registration Requirements</h4>
                    <ul class="text-sm text-blue-700 mt-1 space-y-1">
                        <li>• You must be an existing patient in our system</li>
                        <li>• Your name must match our medical records exactly</li>
                        <li>• You can scan your patient card QR code to auto-fill your ZimHealth-ID</li>
                        <li>• This portal provides read-only access to your medical information</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- QR Code Scanner Modal (same as login) -->
<div id="qr-modal" class="qr-scanner-overlay">
    <div class="qr-scanner-modal">
        <div class="qr-modal-header">
            <div class="qr-header-content">
                <div class="qr-success-indicator">
                    <div class="qr-icon-container">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <div>
                        <h3 class="qr-modal-title">Scan Your ZimHealth-ID QR Code</h3>
                        <p class="qr-modal-subtitle">Position your patient card QR code within camera view</p>
                    </div>
                </div>
            </div>
            <button type="button" id="close-qr-btn" class="qr-close-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="qr-modal-body">
            <div class="qr-video-container">
                <video id="qr-video" autoplay playsinline></video>
                <div class="qr-overlay">
                    <div class="qr-scanner-frame"></div>
                </div>
            </div>
            <div class="qr-status" id="qr-status">
                <i class="fas fa-qrcode"></i>
                <span>Position QR code within the frame</span>
            </div>
        </div>
    </div>
</div>

<style>
/* QR Scanner Modal - Matching Dashboard Patients Page Modal Size */
.qr-scanner-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.6) !important;
    backdrop-filter: blur(8px) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 9999 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
}

.qr-scanner-overlay.active {
    opacity: 1 !important;
    visibility: visible !important;
}

.qr-scanner-modal {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95)) !important;
    backdrop-filter: blur(25px) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 16px !important;
    padding: 2rem !important;
    max-width: 500px !important; /* Match dashboard patients modal size */
    width: 90% !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2) !important;
    transform: scale(0.9) !important;
    transition: transform 0.3s ease !important;
}

.qr-scanner-overlay.active .qr-scanner-modal {
    transform: scale(1) !important;
}

.qr-modal-header {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    margin-bottom: 1.5rem !important;
    padding-bottom: 1rem !important;
    border-bottom: 1px solid rgba(229, 231, 235, 0.3) !important;
}

.qr-header-content {
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important;
}

.qr-success-indicator {
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
}

.qr-icon-container {
    width: 2.5rem !important;
    height: 2.5rem !important;
    background: linear-gradient(135deg, #0284c7, #0369a1) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-size: 1.25rem !important;
}

.qr-modal-title {
    font-size: 1.125rem !important;
    font-weight: 600 !important;
    color: #111827 !important;
    margin: 0 !important;
}

.qr-modal-subtitle {
    font-size: 0.875rem !important;
    color: #6b7280 !important;
    margin: 0.25rem 0 0 0 !important;
}

.qr-close-btn {
    background: none !important;
    border: none !important;
    color: #6b7280 !important;
    cursor: pointer !important;
    padding: 0.5rem !important;
    border-radius: 0.375rem !important;
    transition: all 0.2s ease !important;
    font-size: 1.25rem !important;
}

.qr-close-btn:hover {
    background: rgba(107, 114, 128, 0.1) !important;
    color: #374151 !important;
}

.qr-modal-body {
    text-align: center !important;
}

.qr-video-container {
    position: relative !important;
    margin-bottom: 1rem !important;
}

#qr-video {
    width: 100% !important;
    max-width: 400px !important; /* Limit video size to match modal */
    height: auto !important;
    border-radius: 12px !important;
    background: #000 !important;
}

.qr-overlay {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    pointer-events: none !important;
}

.qr-scanner-frame {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 200px !important;
    height: 200px !important;
    border: 3px solid #0284c7 !important;
    border-radius: 12px !important;
    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.3) !important;
}

.qr-status {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
    padding: 0.75rem !important;
    background: rgba(2, 132, 199, 0.1) !important;
    border-radius: 0.5rem !important;
    color: #0284c7 !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
}

.qr-status.success {
    background: rgba(34, 197, 94, 0.1) !important;
    color: #16a34a !important;
}

.qr-status.error {
    background: rgba(239, 68, 68, 0.1) !important;
    color: #dc2626 !important;
}

.qr-status.warning {
    background: rgba(245, 158, 11, 0.1) !important;
    color: #d97706 !important;
}

/* Responsive design */
@media (max-width: 640px) {
    .qr-scanner-modal {
        max-width: 95% !important;
        padding: 1.5rem !important;
    }
    
    #qr-video {
        max-width: 100% !important;
    }
    
    .qr-scanner-frame {
        width: 150px !important;
        height: 150px !important;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<!-- jsQR Library for QR Code Detection -->
<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>

<script>
// QR Code Scanner for Patient Registration
document.addEventListener('DOMContentLoaded', function() {
    const qrScannerBtn = document.getElementById('qr-scanner-btn');
    const closeQrBtn = document.getElementById('close-qr-btn');
    const qrModal = document.getElementById('qr-modal');

    // Ensure modal is hidden on page load
    if (qrModal) {
        qrModal.classList.remove('active');
        qrModal.style.opacity = '0';
        qrModal.style.visibility = 'hidden';
        qrModal.style.pointerEvents = 'none';
    }

    if (qrScannerBtn) {
        qrScannerBtn.addEventListener('click', function(e) {
            e.preventDefault();
            openQRScanner();
        });
    }

    if (closeQrBtn) {
        closeQrBtn.addEventListener('click', function() {
            closeQRScanner();
        });
    }

    if (qrModal) {
        qrModal.addEventListener('click', function(e) {
            if (e.target === this) closeQRScanner();
        });
    }
});

// QR Scanner functions (same as login but populates registration form)
async function openQRScanner() {
    const qrModal = document.getElementById('qr-modal');
    const qrVideo = document.getElementById('qr-video');

    try {
        const stream = await navigator.mediaDevices.getUserMedia({
            video: {
                facingMode: 'environment',
                width: { ideal: 1920, min: 640 },
                height: { ideal: 1080, min: 480 },
                frameRate: { ideal: 30, min: 15 }
            }
        });

        qrVideo.srcObject = stream;

        // Show modal with both class and inline styles
        qrModal.classList.add('active');
        qrModal.style.opacity = '1';
        qrModal.style.visibility = 'visible';
        qrModal.style.pointerEvents = 'auto';

        updateQRScannerStatus('Scanning for QR codes...', 'info');
        showNotification('QR Scanner activated. Point camera at your patient card QR code.', 'info');

        startQRDetection(qrVideo);

    } catch (error) {
        console.error('Error accessing camera:', error);
        
        let errorMessage = 'Camera access failed. ';
        if (error.name === 'NotAllowedError') {
            errorMessage += 'Please allow camera permissions and try again.';
        } else if (error.name === 'NotFoundError') {
            errorMessage += 'No camera found on this device.';
        } else if (error.name === 'NotSupportedError') {
            errorMessage += 'Camera not supported on this device.';
        } else if (error.name === 'NotReadableError') {
            errorMessage += 'Camera is being used by another application.';
        } else {
            errorMessage += 'Please check your camera settings and try again.';
        }
        
        updateQRScannerStatus(errorMessage, 'error');
        showNotification(errorMessage, 'error');
    }
}

function startQRDetection(video) {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    let isScanning = true;

    function scanFrame() {
        if (!isScanning) return;

        const qrModal = document.getElementById('qr-modal');
        if (!qrModal || !qrModal.classList.contains('active')) {
            isScanning = false;
            return;
        }

        if (video.readyState === video.HAVE_ENOUGH_DATA) {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0, canvas.width, canvas.height);

            const imageData = context.getImageData(0, 0, canvas.width, canvas.height);

            if (typeof jsQR !== 'undefined') {
                const qrCode = jsQR(imageData.data, imageData.width, imageData.height, {
                    inversionAttempts: "attemptBoth",
                });

                if (qrCode && qrCode.data) {
                    console.log('QR Code detected:', qrCode.data);
                    updateQRScannerStatus('QR Code detected! Processing...', 'success');
                    isScanning = false;
                    handleQRCodeDetected(qrCode.data);
                    return;
                }

                updateQRScannerStatus('Scanning... Point camera at QR code', 'info');
            } else {
                console.error('jsQR library not loaded');
                updateQRScannerStatus('QR scanner library not loaded', 'error');
                isScanning = false;
                closeQRScanner();
                showNotification('QR scanner library failed to load. Please refresh the page.', 'error');
                return;
            }
        }

        requestAnimationFrame(scanFrame);
    }

    video.addEventListener('loadedmetadata', () => {
        scanFrame();
    });

    if (video.readyState >= video.HAVE_METADATA) {
        scanFrame();
    }
}

async function handleQRCodeDetected(qrData) {
    closeQRScanner();

    try {
        const response = await fetch(`/patient/ajax/scan-qr/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': getCsrfToken(),
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: `qr_data=${encodeURIComponent(qrData)}`
        });

        const data = await response.json();

        if (data.success) {
            // Populate the registration form
            const patientData = data.patient_data;
            
            document.getElementById('id_zimhealth_id').value = patientData.zimhealth_id;
            document.getElementById('id_first_name').value = patientData.first_name;
            document.getElementById('id_last_name').value = patientData.last_name;
            
            if (patientData.phone_number) {
                document.getElementById('id_phone_number').value = patientData.phone_number;
            }
            if (patientData.email) {
                document.getElementById('id_email').value = patientData.email;
            }
            
            showNotification(`Patient information loaded: ${patientData.first_name} ${patientData.last_name}`, 'success');
            
            // Show detailed patient info panel
            showPatientInfoPanel(patientData);
            
            // Focus on password field
            const passwordField = document.getElementById('id_password1');
            if (passwordField) {
                passwordField.focus();
            }
        } else {
            showNotification(data.error || 'Invalid QR code', 'error');
        }
    } catch (error) {
        console.error('Error processing QR code:', error);
        showNotification('Error processing QR code. Please try again.', 'error');
    }
}

// Show patient info panel for better UX
function showPatientInfoPanel(patientData) {
    // Remove existing panel if any
    const existingPanel = document.getElementById('patient-info-panel');
    if (existingPanel) {
        existingPanel.remove();
    }
    
    // Create info panel
    const panel = document.createElement('div');
    panel.id = 'patient-info-panel';
    panel.className = 'fixed top-20 right-4 z-40 bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm';
    
    // Build account status message with enhanced details
    let accountStatusHtml = '';
    if (patientData.has_account) {
        if (patientData.account_status === 'active') {
            accountStatusHtml = `
                <div class="mb-3 p-2 bg-green-50 border border-green-200 rounded">
                    <div class="flex items-center text-green-800">
                        <i class="fas fa-check-circle mr-2"></i>
                        <span class="text-sm font-medium">Account Already Exists</span>
                    </div>
                    <div class="text-xs text-green-600 mt-1">
                        You can login instead of registering
                    </div>
                </div>
            `;
        } else {
            accountStatusHtml = `
                <div class="mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
                    <div class="flex items-center text-yellow-800">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <span class="text-sm font-medium">Account Needs Attention</span>
                    </div>
                    <div class="text-xs text-yellow-600 mt-1">
                        Account exists but may need verification
                    </div>
                </div>
            `;
        }
    } else {
        accountStatusHtml = `
            <div class="mb-3 p-2 bg-blue-50 border border-blue-200 rounded">
                <div class="flex items-center text-blue-800">
                    <i class="fas fa-info-circle mr-2"></i>
                    <span class="text-sm font-medium">Ready to Register</span>
                </div>
                <div class="text-xs text-blue-600 mt-1">
                    Complete the form below to create your account
                </div>
            </div>
        `;
    }
    
    // Build enhanced user details if available
    let userDetailsHtml = '';
    if (patientData.user_details && Object.keys(patientData.user_details).length > 0) {
        const details = patientData.user_details;
        let verificationStatus = '';
        let lockStatus = '';
        
        if (details.is_verified !== undefined && details.is_verified !== 'N/A') {
            verificationStatus = `
                <div class="flex items-center ${details.is_verified ? 'text-green-600' : 'text-red-600'}">
                    <i class="fas fa-${details.is_verified ? 'check' : 'times'}-circle mr-1"></i>
                    <span class="text-xs">${details.is_verified ? 'Verified' : 'Not Verified'}</span>
                </div>
            `;
        }
        
        if (details.account_locked) {
            lockStatus = `
                <div class="flex items-center text-red-600">
                    <i class="fas fa-lock mr-1"></i>
                    <span class="text-xs">Account Locked</span>
                </div>
            `;
        } else if (details.failed_attempts > 0) {
            lockStatus = `
                <div class="flex items-center text-yellow-600">
                    <i class="fas fa-exclamation-triangle mr-1"></i>
                    <span class="text-xs">${details.failed_attempts} failed attempts</span>
                </div>
            `;
        }
        
        userDetailsHtml = `
            <div class="mt-3 p-2 bg-gray-50 border border-gray-200 rounded">
                <div class="text-xs text-gray-600 space-y-1">
                    <div><strong>Username:</strong> ${details.username || 'N/A'}</div>
                    <div><strong>Email:</strong> ${details.email || 'N/A'}</div>
                    <div><strong>Member Since:</strong> ${details.date_joined || 'N/A'}</div>
                    <div><strong>Last Login:</strong> ${details.last_login || 'N/A'}</div>
                    ${verificationStatus}
                    ${lockStatus}
                </div>
            </div>
        `;
    }
    
    // Build status message section
    let statusMessageHtml = '';
    if (patientData.status_message) {
        statusMessageHtml = `
            <div class="mt-3 p-2 bg-gray-50 border border-gray-200 rounded">
                <div class="text-xs text-gray-700">
                    <i class="fas fa-info-circle mr-1"></i>
                    ${patientData.status_message}
                </div>
            </div>
        `;
    }
    
    panel.innerHTML = `
        <div class="flex items-center justify-between mb-3">
            <h4 class="text-sm font-semibold text-gray-900">Patient Information</h4>
            <button onclick="this.parentElement.parentElement.remove()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        ${accountStatusHtml}
        
        <div class="space-y-2 text-xs text-gray-600">
            <div><strong>Name:</strong> ${patientData.full_name}</div>
            <div><strong>ID:</strong> ${patientData.zimhealth_id}</div>
            <div><strong>Age:</strong> ${patientData.age || 'N/A'}</div>
            <div><strong>Phone:</strong> ${patientData.phone_number || 'N/A'}</div>
            <div><strong>Blood Type:</strong> ${patientData.blood_type || 'N/A'}</div>
            ${patientData.recent_records_count > 0 ? `<div><strong>Recent Records:</strong> ${patientData.recent_records_count}</div>` : ''}
            ${patientData.upcoming_appointments > 0 ? `<div><strong>Upcoming Appointments:</strong> ${patientData.upcoming_appointments}</div>` : ''}
        </div>
        
        ${userDetailsHtml}
        ${statusMessageHtml}
        
        <div class="mt-3 text-xs text-gray-500">
            <i class="fas fa-info-circle mr-1"></i>
            ${patientData.has_account ? 
                (patientData.account_status === 'active' ? 
                    'You already have an account. Consider logging in instead.' : 
                    'Your account exists but may need verification. Try logging in or contact support.'
                ) : 
                'Complete the registration form below to create your patient portal account.'
            }
        </div>
    `;
    
    document.body.appendChild(panel);
    
    // Auto-remove after 20 seconds
    setTimeout(() => {
        if (panel.parentElement) {
            panel.remove();
        }
    }, 20000);
}

function closeQRScanner() {
    const qrModal = document.getElementById('qr-modal');
    const qrVideo = document.getElementById('qr-video');

    if (qrVideo.srcObject) {
        const tracks = qrVideo.srcObject.getTracks();
        tracks.forEach(track => track.stop());
        qrVideo.srcObject = null;
    }

    qrModal.classList.remove('active');

    // Hide modal with both class and inline styles
    qrModal.style.opacity = '0';
    qrModal.style.visibility = 'hidden';
    qrModal.style.pointerEvents = 'none';
}

function updateQRScannerStatus(message, type) {
    const statusElement = document.getElementById('qr-status');
    if (statusElement) {
        const icon = type === 'success' ? 'fa-check-circle' :
                    type === 'error' ? 'fa-exclamation-triangle' :
                    type === 'warning' ? 'fa-exclamation-circle' : 'fa-qrcode';

        statusElement.innerHTML = `<i class="fas ${icon}"></i><span>${message}</span>`;
        statusElement.className = `qr-status ${type}`;
    }
}

function getCsrfToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]').value;
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transition-all duration-300 transform translate-x-full`;
    
    const colors = {
        'success': 'bg-green-500 text-white',
        'error': 'bg-red-500 text-white',
        'warning': 'bg-yellow-500 text-white',
        'info': 'bg-blue-500 text-white'
    };
    
    notification.className += ` ${colors[type] || colors.info}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}
</script>
{% endblock %}
