from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib.auth.views import LoginView, LogoutView
from django.contrib import messages
from django.urls import reverse_lazy
from django.http import JsonResponse
from django.utils import timezone
from django.core.paginator import Paginator
from django.db.models import Q, Count, Max
from django.db import models
from datetime import timedel<PERSON>

from .forms import PatientRegistrationForm, PatientLoginForm, PatientProfileUpdateForm
from .models import PatientUser, PatientLoginAttempt
from api.models import Patient, MedicalRecord, Prescription, Appointment


def patient_portal_required(view_func):
    """Decorator to ensure only patient portal users can access views"""

    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect("patient_portal:login")

        try:
            request.user.patient_portal_profile
        except PatientUser.DoesNotExist:
            messages.error(request, "Access denied. This area is for patients only.")
            return redirect("patient_portal:login")

        return view_func(request, *args, **kwargs)

    return wrapper


def get_client_ip(request):
    """Get client IP address from request"""
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
    if x_forwarded_for:
        ip = x_forwarded_for.split(",")[0].strip()
    else:
        ip = request.META.get("REMOTE_ADDR", "127.0.0.1")
    return ip


class PatientLoginView(LoginView):
    """Custom login view for patient portal"""

    form_class = PatientLoginForm
    template_name = "patient_portal/login.html"
    redirect_authenticated_user = True

    def get_success_url(self):
        return reverse_lazy("patient_portal:dashboard")

    def form_valid(self, form):
        """Handle successful login"""
        response = super().form_valid(form)

        # Update last login for patient user
        try:
            patient_user = self.request.user.patient_portal_profile
            patient_user.record_successful_login()
        except PatientUser.DoesNotExist:
            pass

        messages.success(
            self.request,
            f"Welcome back, {self.request.user.first_name}! Access your medical information below.",
        )
        return response

    def form_invalid(self, form):
        """Handle failed login"""
        username = form.cleaned_data.get("username", "")

        # Log failed attempt
        PatientLoginAttempt.objects.create(
            zimhealth_id_attempted=username,
            ip_address=get_client_ip(self.request),
            user_agent=self.request.META.get("HTTP_USER_AGENT", ""),
            success=False,
            failure_reason="Invalid credentials",
        )

        messages.error(
            self.request, "Invalid ZimHealth-ID or password. Please try again."
        )
        return super().form_invalid(form)


class PatientLogoutView(LogoutView):
    """Custom logout view for patient portal"""

    template_name = "patient_portal/logout.html"
    http_method_names = ["get", "post"]  # Allow both GET and POST

    def dispatch(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            messages.success(
                request, "You have been safely logged out of your patient portal."
            )
        return super().dispatch(request, *args, **kwargs)

    def get(self, request, *args, **kwargs):
        """Handle GET requests for logout"""
        return self.post(request, *args, **kwargs)


def patient_register_view(request):
    """Patient registration view"""
    if request.user.is_authenticated:
        # If already logged in as patient, redirect to dashboard
        if hasattr(request.user, "patient_portal_profile"):
            return redirect("patient_portal:dashboard")
        else:
            # If logged in as healthcare provider, show message
            messages.info(
                request, "Please logout of the healthcare provider interface first."
            )
            return redirect("zhid_auth:logout")

    if request.method == "POST":
        form = PatientRegistrationForm(request.POST)
        if form.is_valid():
            try:
                user = form.save()

                # Get the PatientUser that was created in the form's save method
                try:
                    patient_user = user.patient_portal_profile

                    # Log the registration
                    PatientLoginAttempt.objects.create(
                        patient_user=patient_user,
                        zimhealth_id_attempted=user.username,
                        ip_address=get_client_ip(request),
                        user_agent=request.META.get("HTTP_USER_AGENT", ""),
                        success=True,
                        failure_reason="",
                    )
                except PatientUser.DoesNotExist:
                    # If PatientUser wasn't created properly, log error but continue
                    PatientLoginAttempt.objects.create(
                        patient_user=None,
                        zimhealth_id_attempted=user.username,
                        ip_address=get_client_ip(request),
                        user_agent=request.META.get("HTTP_USER_AGENT", ""),
                        success=True,
                        failure_reason="PatientUser not created",
                    )

                # Auto-login the user using the default backend
                login(
                    request, user, backend="django.contrib.auth.backends.ModelBackend"
                )

                messages.success(
                    request,
                    "Welcome to your ZimHealth-ID patient portal! You can now view your medical information.",
                )
                return redirect("patient_portal:dashboard")

            except Exception as e:
                # If registration fails, show error message
                messages.error(
                    request,
                    f"Registration failed: {str(e)}. Please try again or contact support.",
                )
                # Don't return here, let the form be displayed again with errors
    else:
        form = PatientRegistrationForm()

    return render(request, "patient_portal/register.html", {"form": form})


@patient_portal_required
def patient_dashboard(request):
    """Patient portal dashboard"""
    patient_user = request.user.patient_portal_profile
    patient = patient_user.patient

    # Get recent medical records
    recent_records = patient.medical_records.order_by("-date")[:5]

    # Get upcoming appointments
    upcoming_appointments = patient.appointments.filter(
        status="scheduled", date__gte=timezone.now().date()
    ).order_by("date", "time")[:3]

    # Get active prescriptions
    active_prescriptions = Prescription.objects.filter(
        medical_record__patient=patient, status="active"
    ).order_by("-created_at")[:5]

    # Get statistics
    total_records = patient.medical_records.count()
    total_prescriptions = Prescription.objects.filter(
        medical_record__patient=patient
    ).count()
    total_appointments = patient.appointments.count()

    # Get recent activity (last 30 days)
    thirty_days_ago = timezone.now().date() - timedelta(days=30)
    recent_activity_count = patient.medical_records.filter(
        date__gte=thirty_days_ago
    ).count()

    context = {
        "patient": patient,
        "patient_user": patient_user,
        "recent_records": recent_records,
        "upcoming_appointments": upcoming_appointments,
        "active_prescriptions": active_prescriptions,
        "total_records": total_records,
        "total_prescriptions": total_prescriptions,
        "total_appointments": total_appointments,
        "recent_activity_count": recent_activity_count,
    }

    return render(request, "patient_portal/dashboard.html", context)


@patient_portal_required
def patient_medical_records(request):
    """View all medical records for the patient"""
    patient_user = request.user.patient_portal_profile
    patient = patient_user.patient

    # Get all medical records with pagination
    medical_records = patient.medical_records.order_by("-date")

    # Search functionality
    search_query = request.GET.get("search", "")
    if search_query:
        medical_records = medical_records.filter(
            Q(diagnosis__icontains=search_query)
            | Q(symptoms__icontains=search_query)
            | Q(treatment__icontains=search_query)
            | Q(doctor_name__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(medical_records, 10)
    page_number = request.GET.get("page")
    medical_records = paginator.get_page(page_number)

    context = {
        "patient": patient,
        "medical_records": medical_records,
        "search_query": search_query,
        "total_records": patient.medical_records.count(),
    }

    return render(request, "patient_portal/medical_records.html", context)


@patient_portal_required
def patient_prescriptions(request):
    """View all prescriptions for the patient"""
    patient_user = request.user.patient_portal_profile
    patient = patient_user.patient

    # Get all prescriptions
    prescriptions = (
        Prescription.objects.filter(medical_record__patient=patient)
        .select_related("medical_record")
        .order_by("-created_at")
    )

    # Filter by status
    status_filter = request.GET.get("status", "")
    if status_filter:
        prescriptions = prescriptions.filter(status=status_filter)

    # Search functionality
    search_query = request.GET.get("search", "")
    if search_query:
        prescriptions = prescriptions.filter(
            Q(medication__icontains=search_query)
            | Q(dosage__icontains=search_query)
            | Q(instructions__icontains=search_query)
        )

    # Get statistics
    active_count = Prescription.objects.filter(
        medical_record__patient=patient, status="active"
    ).count()
    completed_count = Prescription.objects.filter(
        medical_record__patient=patient, status="completed"
    ).count()
    total_count = prescriptions.count()

    # Pagination
    paginator = Paginator(prescriptions, 15)
    page_number = request.GET.get("page")
    prescriptions = paginator.get_page(page_number)

    context = {
        "patient": patient,
        "prescriptions": prescriptions,
        "search_query": search_query,
        "status_filter": status_filter,
        "active_count": active_count,
        "completed_count": completed_count,
        "total_count": total_count,
    }

    return render(request, "patient_portal/prescriptions.html", context)


@patient_portal_required
def patient_appointments(request):
    """View all appointments for the patient"""
    patient_user = request.user.patient_portal_profile
    patient = patient_user.patient

    # Get all appointments
    appointments = patient.appointments.order_by("-date", "-time")

    # Filter by status
    status_filter = request.GET.get("status", "")
    if status_filter:
        appointments = appointments.filter(status=status_filter)

    # Filter by date range
    date_filter = request.GET.get("date_range", "")
    today = timezone.now().date()

    if date_filter == "upcoming":
        appointments = appointments.filter(date__gte=today)
    elif date_filter == "past":
        appointments = appointments.filter(date__lt=today)
    elif date_filter == "this_month":
        appointments = appointments.filter(
            date__year=today.year, date__month=today.month
        )

    # Get statistics
    upcoming_count = patient.appointments.filter(
        status="scheduled", date__gte=today
    ).count()
    completed_count = patient.appointments.filter(status="completed").count()
    total_count = appointments.count()

    # Pagination
    paginator = Paginator(appointments, 15)
    page_number = request.GET.get("page")
    appointments = paginator.get_page(page_number)

    context = {
        "patient": patient,
        "appointments": appointments,
        "status_filter": status_filter,
        "date_filter": date_filter,
        "upcoming_count": upcoming_count,
        "completed_count": completed_count,
        "total_count": total_count,
    }

    return render(request, "patient_portal/appointments.html", context)


@patient_portal_required
def patient_doctors(request):
    """View all doctors/healthcare providers the patient has visited"""
    patient_user = request.user.patient_portal_profile
    patient = patient_user.patient

    # Get unique doctors from medical records
    doctors_from_records = (
        patient.medical_records.values("doctor_name", "facility_name")
        .annotate(visit_count=Count("id"), last_visit=Max("date"))
        .order_by("-last_visit")
    )

    # Get unique doctors from appointments
    doctors_from_appointments = (
        patient.appointments.values("doctor_name", "facility_name")
        .annotate(appointment_count=Count("id"), last_appointment=Max("date"))
        .order_by("-last_appointment")
    )

    # Combine and deduplicate doctors
    doctors_dict = {}

    # Add doctors from medical records
    for record in doctors_from_records:
        key = (record["doctor_name"], record["facility_name"])
        if key not in doctors_dict:
            doctors_dict[key] = {
                "doctor_name": record["doctor_name"],
                "facility_name": record["facility_name"],
                "visit_count": record["visit_count"],
                "appointment_count": 0,
                "last_visit": record["last_visit"],
                "last_appointment": None,
            }
        else:
            doctors_dict[key]["visit_count"] = record["visit_count"]
            doctors_dict[key]["last_visit"] = record["last_visit"]

    # Add doctors from appointments
    for appointment in doctors_from_appointments:
        key = (appointment["doctor_name"], appointment["facility_name"])
        if key not in doctors_dict:
            doctors_dict[key] = {
                "doctor_name": appointment["doctor_name"],
                "facility_name": appointment["facility_name"],
                "visit_count": 0,
                "appointment_count": appointment["appointment_count"],
                "last_visit": None,
                "last_appointment": appointment["last_appointment"],
            }
        else:
            doctors_dict[key]["appointment_count"] = appointment["appointment_count"]
            doctors_dict[key]["last_appointment"] = appointment["last_appointment"]

    # Convert to list and sort by most recent interaction
    doctors_list = list(doctors_dict.values())

    def get_date_value(date_value):
        """Convert datetime/date to date for comparison"""
        if date_value is None:
            return timezone.now().date() - timedelta(days=10000)
        if hasattr(date_value, "date"):
            return date_value.date()
        return date_value

    doctors_list.sort(
        key=lambda x: max(
            get_date_value(x["last_visit"]), get_date_value(x["last_appointment"])
        ),
        reverse=True,
    )

    context = {
        "patient": patient,
        "doctors": doctors_list,
        "total_doctors": len(doctors_list),
    }

    return render(request, "patient_portal/doctors.html", context)


@patient_portal_required
def patient_profile(request):
    """Patient profile and contact information view"""
    patient_user = request.user.patient_portal_profile
    patient = patient_user.patient

    if request.method == "POST":
        form = PatientProfileUpdateForm(
            request.POST, instance=request.user, patient_user=patient_user
        )
        if form.is_valid():
            form.save()
            messages.success(
                request, "Your contact information has been updated successfully!"
            )
            return redirect("patient_portal:profile")
    else:
        form = PatientProfileUpdateForm(
            instance=request.user, patient_user=patient_user
        )

    context = {
        "patient": patient,
        "patient_user": patient_user,
        "form": form,
    }

    return render(request, "patient_portal/profile.html", context)


def patient_scan_qr_ajax(request):
    """AJAX endpoint for QR code scanning during patient registration/login - Enhanced for better data inheritance"""
    if request.method != "POST":
        return JsonResponse({"success": False, "error": "Invalid request method"})

    qr_data = request.POST.get("qr_data", "").strip()

    if not qr_data:
        return JsonResponse({"success": False, "error": "No QR data provided"})

    try:
        # Log the QR data for debugging
        print(f"QR Scan attempt - Raw data: {qr_data[:100]}...")

        # Extract ZimHealth ID from QR data (reuse existing function)
        from api.views import extract_zimhealth_id_from_qr

        zimhealth_id = extract_zimhealth_id_from_qr(qr_data)

        if not zimhealth_id:
            print(f"Failed to extract ZimHealth ID from QR data: {qr_data}")
            return JsonResponse(
                {
                    "success": False,
                    "error": "Invalid QR code format. Please scan a valid ZimHealth-ID QR code.",
                }
            )

        print(f"Extracted ZimHealth ID: {zimhealth_id}")

        # Find patient by ZimHealth ID with comprehensive data
        try:
            patient = Patient.objects.get(zimhealth_id=zimhealth_id, is_active=True)
            print(f"Patient found: {patient.full_name} ({patient.zimhealth_id})")
        except Patient.DoesNotExist:
            print(f"Patient not found for ZimHealth ID: {zimhealth_id}")
            return JsonResponse(
                {
                    "success": False,
                    "error": "Patient not found. Please contact your healthcare provider or verify the QR code.",
                }
            )

        # Debug: Check Patient model relationships
        print(f"Patient model debug info:")
        print(f"  - Patient.user: {getattr(patient, 'user', 'No user field')}")
        print(
            f"  - Patient.user.username: {getattr(patient.user, 'username', 'No username') if hasattr(patient, 'user') and patient.user else 'No user'}"
        )
        print(
            f"  - PatientUser.objects.filter(patient=patient).count(): {PatientUser.objects.filter(patient=patient).count()}"
        )

        # Check if patient has a user account
        try:
            # First try to find PatientUser directly
            patient_user = PatientUser.objects.get(patient=patient)
            has_account = True
            # Check if the underlying User account is active and if PatientUser is verified
            account_status = (
                "active"
                if (patient_user.user.is_active and patient_user.is_verified)
                else "inactive"
            )
            print(
                f"PatientUser found: {patient_user.user.username} (User Active: {patient_user.user.is_active}, Verified: {patient_user.is_verified})"
            )
        except PatientUser.DoesNotExist:
            # If no PatientUser, check if Patient has a direct user relationship
            if hasattr(patient, "user") and patient.user:
                has_account = True
                account_status = "active" if patient.user.is_active else "inactive"
                print(
                    f"Patient has direct user relationship: {patient.user.username} (Active: {patient.user.is_active})"
                )
            else:
                has_account = False
                account_status = "none"
                print(
                    f"No PatientUser or direct user relationship found for patient {patient.zimhealth_id}"
                )

        # Get additional patient information for better context
        try:
            # Get recent medical records count
            recent_records_count = MedicalRecord.objects.filter(
                patient=patient, created_at__gte=timezone.now() - timedelta(days=365)
            ).count()

            # Get upcoming appointments
            upcoming_appointments = Appointment.objects.filter(
                patient=patient,
                appointment_date__gte=timezone.now(),
                status__in=["scheduled", "confirmed"],
            ).count()

            print(
                f"Additional info - Recent records: {recent_records_count}, Upcoming appointments: {upcoming_appointments}"
            )

        except Exception as e:
            print(f"Error getting additional patient info: {e}")
            recent_records_count = 0
            upcoming_appointments = 0

        # Get user account details if available
        user_details = {}
        if has_account:
            try:
                if "patient_user" in locals():
                    user_details = {
                        "username": patient_user.user.username,
                        "email": patient_user.user.email,
                        "date_joined": (
                            patient_user.user.date_joined.strftime("%Y-%m-%d")
                            if patient_user.user.date_joined
                            else ""
                        ),
                        "last_login": (
                            patient_user.user.last_login.strftime("%Y-%m-%d %H:%M")
                            if patient_user.user.last_login
                            else "Never"
                        ),
                        "is_verified": patient_user.is_verified,
                        "account_locked": patient_user.is_account_locked(),
                        "failed_attempts": patient_user.failed_login_attempts,
                    }
                elif hasattr(patient, "user") and patient.user:
                    user_details = {
                        "username": patient.user.username,
                        "email": patient.user.email,
                        "date_joined": (
                            patient.user.date_joined.strftime("%Y-%m-%d")
                            if patient.user.date_joined
                            else ""
                        ),
                        "last_login": (
                            patient.user.last_login.strftime("%Y-%m-%d %H:%M")
                            if patient.user.last_login
                            else "Never"
                        ),
                        "is_verified": "N/A",  # Direct user relationship doesn't have verification status
                        "account_locked": False,
                        "failed_attempts": 0,
                    }
                print(f"User account details: {user_details}")
            except Exception as e:
                print(f"Error getting user details: {e}")
                user_details = {}

        # Create a more descriptive account status message
        if has_account:
            if account_status == "active":
                status_message = f"Account is active and ready to use"
            else:
                status_message = f"Account exists but is not active (User active: {user_details.get('is_verified', 'N/A')}, Verified: {user_details.get('is_verified', 'N/A')})"
        else:
            status_message = "No patient portal account found - registration required"

        # Return comprehensive patient data for form population and context
        patient_data = {
            "zimhealth_id": patient.zimhealth_id,
            "first_name": patient.first_name,
            "last_name": patient.last_name,
            "full_name": patient.full_name,
            "phone_number": patient.phone_number or "",
            "email": patient.email or "",
            "date_of_birth": (
                patient.date_of_birth.strftime("%Y-%m-%d")
                if patient.date_of_birth
                else ""
            ),
            "age": patient.age if hasattr(patient, "age") else "",
            "gender": patient.get_gender_display() if patient.gender else "",
            "blood_type": patient.blood_type or "Unknown",
            "emergency_contact": patient.emergency_contact or "",  # Fixed field name
            "allergies": patient.allergies or [],
            "address": patient.address or "",
            "nationality": patient.nationality or "",
            "marital_status": patient.marital_status or "",
            "has_account": has_account,
            "account_status": account_status,
            "status_message": status_message,
            "recent_records_count": recent_records_count,
            "upcoming_appointments": upcoming_appointments,
            "registration_date": (
                patient.registration_date.strftime("%Y-%m-%d")
                if patient.registration_date
                else ""
            ),
            "is_active": patient.is_active,
            "user_details": user_details,  # Add user details to the response
        }

        print(f"Returning patient data: {patient_data}")

        # Create appropriate message based on account status
        if has_account:
            if account_status == "active":
                message = f"Patient {patient.full_name} found successfully. You have an active account. Please enter your password to continue."
            else:
                message = f"Patient {patient.full_name} found successfully. Your account exists but needs verification. Please contact support or try logging in with your password."
        else:
            message = f"Patient {patient.full_name} found successfully. No patient portal account exists yet. Please complete registration to create your account."

        return JsonResponse(
            {"success": True, "patient_data": patient_data, "message": message}
        )

    except Exception as e:
        import traceback

        print(f"Error in patient_scan_qr_ajax: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")

        return JsonResponse(
            {
                "success": False,
                "error": "Error processing QR code. Please try again or contact support if the issue persists.",
            }
        )


@patient_portal_required
def patient_medical_record_detail(request, record_id):
    """Detailed view of a specific medical record"""
    patient_user = request.user.patient_portal_profile
    patient = patient_user.patient

    # Ensure the medical record belongs to this patient
    medical_record = get_object_or_404(MedicalRecord, id=record_id, patient=patient)

    # Get prescriptions for this medical record
    prescriptions = medical_record.prescriptions.all()

    context = {
        "patient": patient,
        "medical_record": medical_record,
        "prescriptions": prescriptions,
    }

    return render(request, "patient_portal/medical_record_detail.html", context)


@patient_portal_required
def patient_prescription_detail(request, prescription_id):
    """Detailed view of a specific prescription"""
    patient_user = request.user.patient_portal_profile
    patient = patient_user.patient

    # Ensure the prescription belongs to this patient
    prescription = get_object_or_404(
        Prescription, id=prescription_id, medical_record__patient=patient
    )

    context = {
        "patient": patient,
        "prescription": prescription,
    }

    return render(request, "patient_portal/prescription_detail.html", context)


@patient_portal_required
def patient_appointment_detail(request, appointment_id):
    """Detailed view of a specific appointment"""
    patient_user = request.user.patient_portal_profile
    patient = patient_user.patient

    # Ensure the appointment belongs to this patient
    appointment = get_object_or_404(Appointment, id=appointment_id, patient=patient)

    context = {
        "patient": patient,
        "appointment": appointment,
    }

    return render(request, "patient_portal/appointment_detail.html", context)
