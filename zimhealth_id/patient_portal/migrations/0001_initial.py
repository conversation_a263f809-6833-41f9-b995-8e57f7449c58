# Generated by Django 5.2.4 on 2025-08-12 10:43

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("api", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="PatientUser",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_verified",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the patient has verified their identity",
                    ),
                ),
                ("registration_date", models.DateTimeField(auto_now_add=True)),
                ("last_login_date", models.DateTimeField(blank=True, null=True)),
                ("failed_login_attempts", models.IntegerField(default=0)),
                ("account_locked_until", models.DateTimeField(blank=True, null=True)),
                ("terms_accepted", models.BooleanField(default=False)),
                ("privacy_policy_accepted", models.BooleanField(default=False)),
                ("terms_accepted_date", models.DateTimeField(blank=True, null=True)),
                (
                    "patient",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="portal_user",
                        to="api.patient",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="patient_portal_profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Patient Portal User",
                "verbose_name_plural": "Patient Portal Users",
                "ordering": ["-registration_date"],
            },
        ),
        migrations.CreateModel(
            name="PatientLoginAttempt",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "zimhealth_id_attempted",
                    models.CharField(
                        help_text="ZimHealth-ID that was attempted for login",
                        max_length=20,
                    ),
                ),
                ("ip_address", models.GenericIPAddressField()),
                ("user_agent", models.TextField(blank=True, null=True)),
                ("success", models.BooleanField(default=False)),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "failure_reason",
                    models.CharField(
                        blank=True, help_text="Reason for login failure", max_length=100
                    ),
                ),
                (
                    "patient_user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="login_attempts",
                        to="patient_portal.patientuser",
                    ),
                ),
            ],
            options={
                "verbose_name": "Patient Login Attempt",
                "verbose_name_plural": "Patient Login Attempts",
                "ordering": ["-timestamp"],
            },
        ),
    ]
