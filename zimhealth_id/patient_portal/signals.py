from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.contrib.auth.models import User
from .models import PatientUser
import logging

logger = logging.getLogger(__name__)


@receiver(post_delete, sender=Pat<PERSON>User)
def cleanup_user_on_patient_user_delete(sender, instance, **kwargs):
    """
    Clean up User object when <PERSON>ient<PERSON><PERSON> is deleted if the User
    has no other relationships and follows ZimHealth-ID format
    """
    user = instance.user

    # Only clean up users with ZimHealth-ID format usernames
    if not user.username.startswith("ZH-"):
        return

    # Check if user has any other important relationships
    has_other_relationships = (
        hasattr(user, "patient_profile")
        or user.is_staff
        or user.is_superuser
        or user.groups.exists()
        or user.user_permissions.exists()
    )

    if not has_other_relationships:
        try:
            logger.info(f"Cleaning up orphaned user: {user.username}")
            user.delete()
        except Exception as e:
            logger.error(f"Failed to cleanup user {user.username}: {e}")
