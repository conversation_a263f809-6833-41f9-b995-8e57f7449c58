from django.urls import path
from . import views

app_name = "patient_portal"

urlpatterns = [
    # Authentication URLs
    path("login/", views.PatientLoginView.as_view(), name="login"),
    path("logout/", views.PatientLogoutView.as_view(), name="logout"),
    path("register/", views.patient_register_view, name="register"),
    # Main portal URLs
    path("", views.patient_dashboard, name="dashboard"),
    path("dashboard/", views.patient_dashboard, name="dashboard_alt"),
    # Medical data views
    path("medical-records/", views.patient_medical_records, name="medical_records"),
    path(
        "medical-records/<uuid:record_id>/",
        views.patient_medical_record_detail,
        name="medical_record_detail",
    ),
    path("prescriptions/", views.patient_prescriptions, name="prescriptions"),
    path(
        "prescriptions/<uuid:prescription_id>/",
        views.patient_prescription_detail,
        name="prescription_detail",
    ),
    path("appointments/", views.patient_appointments, name="appointments"),
    path(
        "appointments/<uuid:appointment_id>/",
        views.patient_appointment_detail,
        name="appointment_detail",
    ),
    path("doctors/", views.patient_doctors, name="doctors"),
    path("profile/", views.patient_profile, name="profile"),
    # AJAX endpoints
    path("ajax/scan-qr/", views.patient_scan_qr_ajax, name="scan_qr_ajax"),
]
