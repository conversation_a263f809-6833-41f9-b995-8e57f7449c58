from django.contrib.auth.backends import ModelBackend
from django.contrib.auth.models import User
from django.utils import timezone
from .models import PatientUser, PatientLoginAttempt
import re


class PatientPortalBackend(ModelBackend):
    """
    Custom authentication backend for patient portal.
    Authenticates patients using their ZimHealth-ID as username.
    """

    def authenticate(self, request, username=None, password=None, **kwargs):
        """Authenticate patient using ZimHealth-ID - only for patient portal login"""
        if not username or not password:
            return None

        # Only handle ZimHealth-ID format usernames for patient portal
        zimhealth_id = username.strip().upper()
        if not re.match(r"^ZH-\d{4}-\d{6}$", zimhealth_id):
            # Not a ZimHealth-ID format, let other backends handle it
            return None

        try:
            # Find user by ZimHealth-ID (stored as username)
            user = User.objects.get(username=zimhealth_id)

            # Check if user has patient profile
            try:
                patient_user = user.patient_portal_profile
            except PatientUser.DoesNotExist:
                self._log_failed_attempt(
                    request, zimhealth_id, "No patient portal account"
                )
                return None

            # Check if account is locked
            if patient_user.is_account_locked():
                self._log_failed_attempt(request, zimhealth_id, "Account locked")
                return None

            # Verify password
            if user.check_password(password):
                # Successful login
                patient_user.record_successful_login()
                self._log_successful_attempt(request, patient_user, zimhealth_id)
                return user
            else:
                # Wrong password
                patient_user.record_failed_login()
                self._log_failed_attempt(
                    request, zimhealth_id, "Invalid password", patient_user
                )
                return None

        except User.DoesNotExist:
            self._log_failed_attempt(
                request, zimhealth_id, "ZimHealth-ID not registered"
            )
            return None

    def get_user(self, user_id):
        """Get user by ID - allow users during registration process"""
        try:
            user = User.objects.get(pk=user_id)
            # During registration, user might not have patient_portal_profile yet
            # Allow the user if they have a ZimHealth-ID format username or already have a profile
            if hasattr(user, "patient_portal_profile") or (
                user.username and user.username.startswith("ZH-")
            ):
                return user
        except User.DoesNotExist:
            pass
        return None

    def _log_failed_attempt(self, request, zimhealth_id, reason, patient_user=None):
        """Log failed login attempt"""
        ip_address = self._get_client_ip(request)
        user_agent = request.META.get("HTTP_USER_AGENT", "") if request else ""

        PatientLoginAttempt.objects.create(
            patient_user=patient_user,
            zimhealth_id_attempted=zimhealth_id,
            ip_address=ip_address,
            user_agent=user_agent,
            success=False,
            failure_reason=reason,
        )

    def _log_successful_attempt(self, request, patient_user, zimhealth_id):
        """Log successful login attempt"""
        ip_address = self._get_client_ip(request)
        user_agent = request.META.get("HTTP_USER_AGENT", "") if request else ""

        PatientLoginAttempt.objects.create(
            patient_user=patient_user,
            zimhealth_id_attempted=zimhealth_id,
            ip_address=ip_address,
            user_agent=user_agent,
            success=True,
        )

    def _get_client_ip(self, request):
        """Get client IP address from request"""
        if not request:
            return "127.0.0.1"

        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0].strip()
        else:
            ip = request.META.get("REMOTE_ADDR", "127.0.0.1")
        return ip


class PatientPortalMiddleware:
    """
    Middleware to ensure only patient portal users can access patient portal views
    and regular users cannot access patient portal.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Check if this is a patient portal request
        if request.path.startswith("/patient/"):
            if request.user.is_authenticated:
                # Ensure user has patient profile
                if not hasattr(request.user, "patient_portal_profile"):
                    from django.contrib.auth import logout
                    from django.shortcuts import redirect
                    from django.contrib import messages

                    logout(request)
                    messages.error(
                        request, "Access denied. This area is for patients only."
                    )
                    return redirect("patient_portal:login")

        # Check if regular user is trying to access main app
        elif request.path.startswith("/api/") or request.path.startswith("/auth/"):
            if request.user.is_authenticated and hasattr(
                request.user, "patient_portal_profile"
            ):
                # Patient users should not access main healthcare provider interface
                from django.shortcuts import redirect
                from django.contrib import messages

                messages.info(
                    request,
                    "Please use the patient portal to access your medical information.",
                )
                return redirect("patient_portal:dashboard")

        response = self.get_response(request)
        return response
