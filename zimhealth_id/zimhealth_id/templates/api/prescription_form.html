{% extends 'base.html' %}

{% block title %}{{ title }} - ZimHealth-ID{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}?v=**********">
<link rel="stylesheet" href="{% static 'assets/css/patients.css' %}?v=**********">
{% endblock %}

{% block content %}
<div class="patients-container">
    <!-- Professional Prescription Header - EXACT Mirror of Patient Form -->
    <div class="patients-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ title }}</h1>
                        <p class="text-gray-600 mt-1 text-sm">
                            {% if medical_record %}For {{ medical_record.patient.full_name }}{% else %}Create a new prescription{% endif %}
                        </p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:prescriptions' %}" class="government-filter-button">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Prescriptions
                        </a>
                        <div class="add-patient-button flex items-center space-x-2">
                            <i class="fas fa-pills"></i>
                            <span>New Prescription</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Professional Prescription Content - Layout as per wireframe -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 relative z-10">
        <form method="post">
            {% csrf_token %}
            
            <!-- Medical Record Selection -->
            {% if not medical_record %}
            <div class="patients-table-card mb-6">
                <div class="patients-table-header px-6 py-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Medical Record Selection</h3>
                        <div class="flex items-center space-x-2 text-sm text-gray-500">
                            <i class="fas fa-file-medical"></i>
                            <span>Select Record</span>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Search Medical Record
                        </label>
                        <div class="flex gap-3">
                            <div class="relative flex-1">
                                <input type="text" id="record-search"
                                       class="government-search-input-compact w-full pl-10"
                                       placeholder="Search by patient name or record">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                            </div>
                            <button type="button" id="qr-scanner-btn" class="government-filter-button flex items-center space-x-2">
                                <i class="fas fa-qrcode"></i>
                                <span>Scan QR</span>
                            </button>
                        </div>
                    </div>
                    <select name="medical_record" class="government-search-input-compact w-full" required>
                        <option value="">Select a medical record</option>
                        {% for choice in form.medical_record.field.queryset %}
                            <option value="{{ choice.id }}" {% if form.medical_record.value == choice.id %}selected{% endif %}>
                                {{ choice.patient.full_name }} - {{ choice.date|date:"M d, Y" }} - {{ choice.diagnosis|truncatechars:50 }}
                            </option>
                        {% endfor %}
                    </select>
                    {% if form.medical_record.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            {% for error in form.medical_record.errors %}
                                <p class="flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                </p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Medication Information -->
            <div class="patients-table-card mb-6">
                <div class="patients-table-header px-6 py-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Medication Information</h3>
                        <div class="flex items-center space-x-2 text-sm text-gray-500">
                            <i class="fas fa-pills"></i>
                            <span>Drug Details</span>
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="patients-table w-full">
                        <colgroup>
                            <col style="width: 30%;">
                            <col style="width: 70%;">
                        </colgroup>
                        <tbody>
                            <tr>
                                <td class="font-semibold text-gray-700">Medication Name *</td>
                                <td>
                                    <input type="text" name="medication" value="{{ form.medication.value|default:'' }}" class="government-search-input-compact w-full" placeholder="Enter medication name" required>
                                    {% if form.medication.errors %}
                                        <div class="text-red-600 text-xs mt-1">
                                            {% for error in form.medication.errors %}
                                                <p>{{ error }}</p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Dosage *</td>
                                <td>
                                    <input type="text" name="dosage" value="{{ form.dosage.value|default:'' }}" class="government-search-input-compact w-full" placeholder="e.g., 500mg" required>
                                    {% if form.dosage.errors %}
                                        <div class="text-red-600 text-xs mt-1">
                                            {% for error in form.dosage.errors %}
                                                <p>{{ error }}</p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Frequency *</td>
                                <td>
                                    <div class="space-y-3">
                                        <!-- Frequency Type Selection -->
                                        <div class="flex space-x-4">
                                            <label class="flex items-center">
                                                <input type="radio" name="frequency_type" value="predefined" class="mr-2" checked>
                                                <span class="text-sm">Use predefined frequency</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="radio" name="frequency_type" value="custom" class="mr-2">
                                                <span class="text-sm">Enter custom frequency</span>
                                            </label>
                                        </div>

                                        <!-- Predefined Frequency -->
                                        <div id="predefined-frequency-container">
                                            <select name="frequency" id="predefined-frequency" class="government-search-input-compact w-full">
                                                <option value="">Select frequency</option>
                                                <option value="once_daily" {% if form.frequency.value == 'once_daily' %}selected{% endif %}>Once Daily</option>
                                                <option value="twice_daily" {% if form.frequency.value == 'twice_daily' %}selected{% endif %}>Twice Daily</option>
                                                <option value="three_times_daily" {% if form.frequency.value == 'three_times_daily' %}selected{% endif %}>Three Times Daily</option>
                                                <option value="four_times_daily" {% if form.frequency.value == 'four_times_daily' %}selected{% endif %}>Four Times Daily</option>
                                                <option value="every_4_hours" {% if form.frequency.value == 'every_4_hours' %}selected{% endif %}>Every 4 Hours</option>
                                                <option value="every_6_hours" {% if form.frequency.value == 'every_6_hours' %}selected{% endif %}>Every 6 Hours</option>
                                                <option value="every_8_hours" {% if form.frequency.value == 'every_8_hours' %}selected{% endif %}>Every 8 Hours</option>
                                                <option value="every_12_hours" {% if form.frequency.value == 'every_12_hours' %}selected{% endif %}>Every 12 Hours</option>
                                                <option value="as_needed" {% if form.frequency.value == 'as_needed' %}selected{% endif %}>As Needed</option>
                                                <option value="before_meals" {% if form.frequency.value == 'before_meals' %}selected{% endif %}>Before Meals</option>
                                                <option value="after_meals" {% if form.frequency.value == 'after_meals' %}selected{% endif %}>After Meals</option>
                                                <option value="at_bedtime" {% if form.frequency.value == 'at_bedtime' %}selected{% endif %}>At Bedtime</option>
                                                <option value="other" {% if form.frequency.value == 'other' %}selected{% endif %}>Other</option>
                                            </select>
                                        </div>

                                        <!-- Custom Frequency -->
                                        <div id="custom-frequency-container" style="display: none;">
                                            <input type="text" name="custom_frequency" id="custom-frequency"
                                                   value="{{ form.custom_frequency.value|default:'' }}"
                                                   class="government-search-input-compact w-full"
                                                   placeholder="e.g., 3 times per day for 5 days, Every 2 hours as needed">
                                        </div>
                                    </div>

                                    {% if form.frequency.errors %}
                                        <div class="text-red-600 text-xs mt-1">
                                            {% for error in form.frequency.errors %}
                                                <p>{{ error }}</p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    {% if form.custom_frequency.errors %}
                                        <div class="text-red-600 text-xs mt-1">
                                            {% for error in form.custom_frequency.errors %}
                                                <p>{{ error }}</p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Duration *</td>
                                <td>
                                    <input type="text" name="duration" value="{{ form.duration.value|default:'' }}" class="government-search-input-compact w-full" placeholder="e.g., 7 days" required>
                                    {% if form.duration.errors %}
                                        <div class="text-red-600 text-xs mt-1">
                                            {% for error in form.duration.errors %}
                                                <p>{{ error }}</p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Quantity Prescribed</td>
                                <td>
                                    <input type="number" name="quantity_prescribed" value="{{ form.quantity_prescribed.value|default:'' }}" class="government-search-input-compact w-full" placeholder="Quantity">
                                    {% if form.quantity_prescribed.errors %}
                                        <div class="text-red-600 text-xs mt-1">
                                            {% for error in form.quantity_prescribed.errors %}
                                                <p>{{ error }}</p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Prescription Details -->
            <div class="patients-table-card mb-6">
                <div class="patients-table-header px-6 py-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Prescription Details</h3>
                        <div class="flex items-center space-x-2 text-sm text-gray-500">
                            <i class="fas fa-clipboard"></i>
                            <span>Schedule & Status</span>
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="patients-table w-full">
                        <colgroup>
                            <col style="width: 30%;">
                            <col style="width: 70%;">
                        </colgroup>
                        <tbody>
                            <tr>
                                <td class="font-semibold text-gray-700">Start Date *</td>
                                <td>
                                    <input type="date" name="start_date" value="{{ form.start_date.value|default:'' }}" class="government-search-input-compact w-full" required>
                                    {% if form.start_date.errors %}
                                        <div class="text-red-600 text-xs mt-1">
                                            {% for error in form.start_date.errors %}
                                                <p>{{ error }}</p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">End Date</td>
                                <td>
                                    <input type="date" name="end_date" value="{{ form.end_date.value|default:'' }}" class="government-search-input-compact w-full">
                                    {% if form.end_date.errors %}
                                        <div class="text-red-600 text-xs mt-1">
                                            {% for error in form.end_date.errors %}
                                                <p>{{ error }}</p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Refills Allowed</td>
                                <td>
                                    <input type="number" name="refills_allowed" value="{{ form.refills_allowed.value|default:'0' }}" class="government-search-input-compact w-full" min="0" placeholder="0">
                                    {% if form.refills_allowed.errors %}
                                        <div class="text-red-600 text-xs mt-1">
                                            {% for error in form.refills_allowed.errors %}
                                                <p>{{ error }}</p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Status</td>
                                <td>
                                    <select name="status" class="government-search-input-compact w-full">
                                        <option value="active" {% if form.status.value == 'active' %}selected{% endif %}>Active</option>
                                        <option value="completed" {% if form.status.value == 'completed' %}selected{% endif %}>Completed</option>
                                        <option value="discontinued" {% if form.status.value == 'discontinued' %}selected{% endif %}>Discontinued</option>
                                    </select>
                                    {% if form.status.errors %}
                                        <div class="text-red-600 text-xs mt-1">
                                            {% for error in form.status.errors %}
                                                <p>{{ error }}</p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Special Instructions</td>
                                <td>
                                    <textarea name="instructions" class="government-search-input-compact w-full" rows="2" placeholder="Special instructions...">{{ form.instructions.value|default:'' }}</textarea>
                                    {% if form.instructions.errors %}
                                        <div class="text-red-600 text-xs mt-1">
                                            {% for error in form.instructions.errors %}
                                                <p>{{ error }}</p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Non-field Errors -->
            {% if form.non_field_errors %}
                <div class="patients-table-card mb-6">
                    <div class="p-6 bg-red-50">
                        {% for error in form.non_field_errors %}
                            <p class="text-red-800 text-sm flex items-center">
                                <i class="fas fa-exclamation-triangle mr-2"></i>{{ error }}
                            </p>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- Form Actions -->
            <div class="flex flex-col sm:flex-row gap-4 pt-6">
                <button type="submit" class="add-patient-button flex-1 flex items-center justify-center space-x-2">
                    <i class="fas fa-prescription-bottle-alt"></i>
                    <span>Create Prescription</span>
                </button>
                
                <a href="{% url 'api:prescriptions' %}" class="government-filter-button flex-1 flex items-center justify-center space-x-2">
                    <i class="fas fa-times"></i>
                    <span>Cancel</span>
                </a>
            </div>
        </form>
    </div>
</div>
</div>

<!-- QR Code Scanner Modal - ZimHealth-ID Design Language -->
<div id="qr-modal" class="qr-scanner-overlay">
    <div class="qr-scanner-modal">
        <div class="qr-modal-header">
            <div class="qr-header-content">
                <div class="qr-success-indicator">
                    <div class="qr-icon-container">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <div>
                        <h3 class="qr-modal-title">Scan Patient QR Code</h3>
                        <p class="qr-modal-subtitle">Position QR code within camera view</p>
                    </div>
                </div>
            </div>
            <button type="button" id="close-qr-btn" class="qr-close-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="qr-modal-body">
            <div class="qr-video-container">
                <video id="qr-video" autoplay playsinline></video>
                <div class="qr-overlay">
                    <div class="qr-scanner-frame"></div>
                </div>
            </div>
            <div class="qr-status" id="qr-status">
                <i class="fas fa-qrcode"></i>
                <span>Position QR code within the frame</span>
            </div>
        </div>
    </div>
</div>

<style>
/* QR Scanner Modal - ZimHealth-ID Design Language */
.qr-scanner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    z-index: 9999;
    display: none;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.qr-scanner-overlay.active {
    display: flex;
    opacity: 1;
    visibility: visible;
}

/* Main Modal Container - Matching patient success modal */
.qr-scanner-modal {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    transform: scale(0.95) translateY(20px);
    transition: transform 0.3s ease;
    position: relative;
}

.qr-scanner-overlay.active .qr-scanner-modal {
    transform: scale(1) translateY(0);
}

/* Header Section - Matching established pattern */
.qr-modal-header {
    background: #f9fafb;
    border-bottom: 2px solid #e5e7eb;
    padding: 1rem 1.5rem;
    position: relative;
}

.qr-header-content {
    display: flex;
    align-items: center;
    justify-content: center;
}

.qr-success-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
}

.qr-icon-container {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.15), rgba(14, 165, 233, 0.05));
    border: 1px solid rgba(14, 165, 233, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #0ea5e9;
    font-size: 20px;
}

.qr-modal-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: #111827;
    margin: 0;
    line-height: 1.2;
}

.qr-modal-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
    margin-top: 2px;
}

.qr-close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    background: #ffffff;
    border: 1px solid #d1d5db;
    color: #6b7280;
    width: 32px;
    height: 32px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.qr-close-btn:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
}

/* Modal Body */
.qr-modal-body {
    padding: 1.5rem;
}

/* Video Container - Professional styling */
.qr-video-container {
    position: relative;
    width: 100%;
    height: 320px;
    background: #000000;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 1rem;
    border: 2px solid #e5e7eb;
}

#qr-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.qr-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
}

.qr-scanner-frame {
    width: 220px;
    height: 220px;
    border: 3px solid #22c55e;
    border-radius: 12px;
    position: relative;
    animation: qrPulse 2s infinite;
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
}

.qr-scanner-frame::before,
.qr-scanner-frame::after {
    content: '';
    position: absolute;
    width: 24px;
    height: 24px;
    border: 4px solid #22c55e;
    border-radius: 4px;
}

.qr-scanner-frame::before {
    top: -4px;
    left: -4px;
    border-right: none;
    border-bottom: none;
}

.qr-scanner-frame::after {
    bottom: -4px;
    right: -4px;
    border-left: none;
    border-top: none;
}

/* Status Display */
.qr-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
    transition: all 0.3s ease;
}

.qr-status.success {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    border: 1px solid #86efac;
    color: #166534;
}

.qr-status.error {
    background: linear-gradient(135deg, #fef2f2, #fecaca);
    border: 1px solid #fca5a5;
    color: #dc2626;
}

.qr-status.warning {
    background: linear-gradient(135deg, #fefce8, #fef3c7);
    border: 1px solid #fde047;
    color: #ca8a04;
}

.qr-status.info {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    border: 1px solid #93c5fd;
    color: #2563eb;
}

@keyframes qrPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
        box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.02);
        box-shadow: 0 0 30px rgba(34, 197, 94, 0.5);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .qr-scanner-modal {
        max-width: 95%;
        margin: 1rem;
    }

    .qr-video-container {
        height: 280px;
    }

    .qr-scanner-frame {
        width: 180px;
        height: 180px;
    }

    .qr-modal-header {
        padding: 1rem;
    }

    .qr-modal-body {
        padding: 1rem;
    }
}
</style>

{% endblock %}

{% block extra_js %}
<script>
// Medical record search functionality
document.getElementById('record-search')?.addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const select = document.querySelector('select[name="medical_record"]');
    const options = select.querySelectorAll('option');

    options.forEach(option => {
        if (option.value === '') return; // Skip the default option
        const text = option.textContent.toLowerCase();
        option.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

// Frequency type switching functionality
document.addEventListener('DOMContentLoaded', function() {
    const frequencyTypeRadios = document.querySelectorAll('input[name="frequency_type"]');
    const predefinedContainer = document.getElementById('predefined-frequency-container');
    const customContainer = document.getElementById('custom-frequency-container');
    const predefinedSelect = document.getElementById('predefined-frequency');
    const customInput = document.getElementById('custom-frequency');

    // Initialize based on existing data
    if (customInput.value) {
        document.querySelector('input[name="frequency_type"][value="custom"]').checked = true;
        predefinedContainer.style.display = 'none';
        customContainer.style.display = 'block';
    }

    frequencyTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'predefined') {
                predefinedContainer.style.display = 'block';
                customContainer.style.display = 'none';
                customInput.value = '';
            } else {
                predefinedContainer.style.display = 'none';
                customContainer.style.display = 'block';
                predefinedSelect.value = '';
            }
        });
    });
});

// QR Code Scanner Functionality
document.getElementById('qr-scanner-btn')?.addEventListener('click', function() {
    openQRScanner();
});

document.getElementById('close-qr-btn')?.addEventListener('click', function() {
    closeQRScanner();
});

// Close modal when clicking outside
document.getElementById('qr-modal')?.addEventListener('click', function(e) {
    if (e.target === this) closeQRScanner();
});

// Open QR Code Scanner
async function openQRScanner() {
    const qrModal = document.getElementById('qr-modal');
    const qrVideo = document.getElementById('qr-video');

    try {
        const stream = await navigator.mediaDevices.getUserMedia({
            video: {
                facingMode: 'environment',
                width: { ideal: 1920, min: 640 },
                height: { ideal: 1080, min: 480 },
                frameRate: { ideal: 30, min: 15 }
            }
        });

        qrVideo.srcObject = stream;
        qrModal.classList.add('active');

        // Update status
        updateQRScannerStatus('Scanning for QR codes...', 'info');
        showNotification('QR Scanner activated. Point camera at patient QR code.', 'info');

        // Start QR code detection
        startQRDetection(qrVideo);

    } catch (error) {
        console.error('Error accessing camera:', error);

        let errorMessage = 'Camera access failed. ';
        if (error.name === 'NotAllowedError') {
            errorMessage += 'Please allow camera permissions and try again.';
        } else if (error.name === 'NotFoundError') {
            errorMessage += 'No camera found on this device.';
        } else if (error.name === 'NotSupportedError') {
            errorMessage += 'Camera not supported on this device.';
        } else if (error.name === 'NotReadableError') {
            errorMessage += 'Camera is being used by another application.';
        } else {
            errorMessage += 'Please check your camera settings and try again.';
        }

        updateQRScannerStatus(errorMessage, 'error');
        showNotification(errorMessage, 'error');
    }
}

// Start QR Code Detection
function startQRDetection(video) {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    let isScanning = true;

    function scanFrame() {
        if (!isScanning) return;

        const qrModal = document.getElementById('qr-modal');
        if (!qrModal || !qrModal.classList.contains('active')) {
            isScanning = false;
            return;
        }

        if (video.readyState === video.HAVE_ENOUGH_DATA) {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0, canvas.width, canvas.height);

            const imageData = context.getImageData(0, 0, canvas.width, canvas.height);

            // Use jsQR library to detect QR codes
            if (typeof jsQR !== 'undefined') {
                const qrCode = jsQR(imageData.data, imageData.width, imageData.height, {
                    inversionAttempts: "attemptBoth",
                });

                if (qrCode && qrCode.data) {
                    console.log('QR Code detected:', qrCode.data);
                    updateQRScannerStatus('QR Code detected! Processing...', 'success');
                    isScanning = false;
                    handleQRCodeDetected(qrCode.data);
                    return;
                }

                // Update status to show active scanning
                updateQRScannerStatus('Scanning... Point camera at QR code', 'info');
            } else {
                // jsQR library not loaded - show error
                console.error('jsQR library not loaded');
                updateQRScannerStatus('QR scanner library not loaded', 'error');
                isScanning = false;
                closeQRScanner();
                showNotification('QR scanner library failed to load. Please refresh the page.', 'error');
                return;
            }
        }

        // Continue scanning
        requestAnimationFrame(scanFrame);
    }

    // Start scanning when video is ready
    video.addEventListener('loadedmetadata', () => {
        scanFrame();
    });

    // If video is already loaded, start immediately
    if (video.readyState >= video.HAVE_METADATA) {
        scanFrame();
    }

    // Stop scanning when modal is closed
    const qrModal = document.getElementById('qr-modal');
    if (qrModal) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (!qrModal.classList.contains('active')) {
                        isScanning = false;
                    }
                }
            });
        });
        observer.observe(qrModal, { attributes: true });
    }
}

// Close QR Code Scanner
function closeQRScanner() {
    const qrModal = document.getElementById('qr-modal');
    const qrVideo = document.getElementById('qr-video');

    if (qrVideo.srcObject) {
        const tracks = qrVideo.srcObject.getTracks();
        tracks.forEach(track => track.stop());
        qrVideo.srcObject = null;
    }

    qrModal.classList.remove('active');
}

// Auto-calculate end date based on duration
document.querySelector('input[name="duration"]')?.addEventListener('input', function(e) {
    const duration = e.target.value.toLowerCase();
    const startDateInput = document.querySelector('input[name="start_date"]');
    const endDateInput = document.querySelector('input[name="end_date"]');

    if (startDateInput.value && duration) {
        const startDate = new Date(startDateInput.value);
        let endDate = new Date(startDate);

        // Simple duration parsing
        if (duration.includes('day')) {
            const days = parseInt(duration.match(/\d+/)?.[0] || 0);
            endDate.setDate(startDate.getDate() + days);
        } else if (duration.includes('week')) {
            const weeks = parseInt(duration.match(/\d+/)?.[0] || 0);
            endDate.setDate(startDate.getDate() + (weeks * 7));
        } else if (duration.includes('month')) {
            const months = parseInt(duration.match(/\d+/)?.[0] || 0);
            endDate.setMonth(startDate.getMonth() + months);
        }

        if (endDate > startDate) {
            endDateInput.value = endDate.toISOString().split('T')[0];
        }
    }
});

// Handle QR Code Detection
async function handleQRCodeDetected(qrData) {
    closeQRScanner();

    // Extract ZimHealth ID from QR data
    let zimhealthId = extractZimHealthId(qrData);

    if (!zimhealthId) {
        showNotification('Invalid QR code format. Please scan a valid patient QR code.', 'error');
        return;
    }

    showNotification('Looking up patient medical records...', 'info');

    try {
        // Send the QR data to the server for processing
        const response = await fetch(`/api/ajax/scan-qr/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': getCsrfToken(),
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: `qr_data=${encodeURIComponent(qrData)}`
        });

        const data = await response.json();

        if (data.success) {
            // Patient found - now find their medical records and populate the select
            populateMedicalRecordFromQR(data.patient, zimhealthId);
            showNotification(`Found patient: ${data.patient.full_name} (${zimhealthId})`, 'success');
        } else {
            showNotification(data.error || 'Patient not found with this QR code', 'error');
        }
    } catch (error) {
        console.error('Error processing QR code:', error);
        showNotification('Error processing QR code. Please try again.', 'error');
    }
}

// Populate medical record selection from QR scan
function populateMedicalRecordFromQR(patient, zimhealthId) {
    const recordSelect = document.querySelector('select[name="medical_record"]');
    const searchInput = document.getElementById('record-search');

    if (recordSelect && searchInput) {
        // Set search input to patient name to filter records
        searchInput.value = patient.full_name;

        // Trigger the search to filter the dropdown
        const event = new Event('input', { bubbles: true });
        searchInput.dispatchEvent(event);

        // Try to find and select the most recent medical record for this patient
        const options = recordSelect.querySelectorAll('option');
        let selectedOption = null;

        for (const option of options) {
            if (option.textContent.toLowerCase().includes(patient.full_name.toLowerCase())) {
                if (!selectedOption) {
                    selectedOption = option; // Select the first (most recent) match
                }
            }
        }

        if (selectedOption) {
            recordSelect.value = selectedOption.value;
            showNotification(`Selected medical record for ${patient.full_name}`, 'success');
        } else {
            showNotification(`Patient found but no medical records available. Please create a medical record first.`, 'warning');
        }
    }
}

// Extract ZimHealth ID from QR code data - Enhanced for real-world usage
function extractZimHealthId(qrData) {
    if (!qrData || typeof qrData !== 'string') {
        return null;
    }

    // Clean the data
    const cleanData = qrData.trim();

    // Direct ZimHealth ID format: ZH-YYYY-XXXXXX
    if (cleanData.match(/^ZH-\d{4}-\d{6}$/)) {
        return cleanData;
    }

    // Multi-line format with ZimHealth-ID prefix
    const lines = cleanData.split(/[\n\r]+/);
    for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine.startsWith('ZimHealth-ID:')) {
            const id = trimmedLine.replace('ZimHealth-ID:', '').trim();
            if (id.match(/^ZH-\d{4}-\d{6}$/)) {
                return id;
            }
        }
        // Also check for variations like "ID:" or "Patient ID:"
        if (trimmedLine.match(/^(ID|Patient\s*ID):\s*ZH-\d{4}-\d{6}$/i)) {
            const match = trimmedLine.match(/ZH-\d{4}-\d{6}/);
            if (match) {
                return match[0];
            }
        }
    }

    // Try to find ZH-YYYY-XXXXXX pattern anywhere in the string
    const match = cleanData.match(/ZH-\d{4}-\d{6}/);
    if (match) {
        return match[0];
    }

    // Check for JSON format
    try {
        const jsonData = JSON.parse(cleanData);
        if (jsonData.zimhealth_id && jsonData.zimhealth_id.match(/^ZH-\d{4}-\d{6}$/)) {
            return jsonData.zimhealth_id;
        }
        if (jsonData.id && jsonData.id.match(/^ZH-\d{4}-\d{6}$/)) {
            return jsonData.id;
        }
    } catch (e) {
        // Not JSON, continue
    }

    return null;
}

// Update QR Scanner Status
function updateQRScannerStatus(message, type) {
    const statusElement = document.getElementById('qr-status');
    if (statusElement) {
        const icon = type === 'success' ? 'fa-check-circle' :
                    type === 'error' ? 'fa-exclamation-triangle' :
                    type === 'warning' ? 'fa-exclamation-circle' : 'fa-qrcode';

        statusElement.innerHTML = `<i class="fas ${icon}"></i><span>${message}</span>`;

        // Update color based on type
        statusElement.className = `qr-status ${type}`;
    }
}



// Get CSRF token
function getCsrfToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]').value;
}

// Show notification function
function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transition-all duration-300 transform translate-x-full`;

    // Set colors based on type
    const colors = {
        'success': 'bg-green-500 text-white',
        'error': 'bg-red-500 text-white',
        'warning': 'bg-yellow-500 text-white',
        'info': 'bg-blue-500 text-white'
    };

    notification.className += ` ${colors[type] || colors.info}`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Auto-calculate end date based on duration
document.querySelector('input[name="duration"]')?.addEventListener('input', function(e) {
    const duration = e.target.value.toLowerCase();
    const startDateInput = document.querySelector('input[name="start_date"]');
    const endDateInput = document.querySelector('input[name="end_date"]');

    if (startDateInput.value && duration) {
        const startDate = new Date(startDateInput.value);
        let endDate = new Date(startDate);

        // Simple duration parsing
        if (duration.includes('day')) {
            const days = parseInt(duration.match(/\d+/)?.[0] || 0);
            endDate.setDate(startDate.getDate() + days);
        } else if (duration.includes('week')) {
            const weeks = parseInt(duration.match(/\d+/)?.[0] || 0);
            endDate.setDate(startDate.getDate() + (weeks * 7));
        } else if (duration.includes('month')) {
            const months = parseInt(duration.match(/\d+/)?.[0] || 0);
            endDate.setMonth(startDate.getMonth() + months);
        }

        if (endDate > startDate) {
            endDateInput.value = endDate.toISOString().split('T')[0];
        }
    }
});
</script>

<!-- jsQR Library for QR Code Detection -->
<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
{% endblock %}
