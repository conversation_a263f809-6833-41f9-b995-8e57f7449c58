{% extends 'base.html' %}
{% load static %}

{% block title %}Prescription Management - ZimHealth-ID{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}?v=**********">
<link rel="stylesheet" href="{% static 'assets/css/prescriptions.css' %}?v=**********">
<style>
/* Simple clean table styling */
table {
    font-family: Arial, sans-serif;
}
</style>
{% endblock %}

{% block content %}
{% csrf_token %}
<div class="prescriptions-container">
    <!-- Professional Prescriptions Header -->
    <div class="prescriptions-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Prescription Management</h1>
                        <p class="text-gray-600 mt-1 text-sm">Professional medication management and prescription tracking system</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:prescription_create' %}" class="new-prescription-button flex items-center space-x-2">
                            <i class="fas fa-prescription-bottle-alt"></i>
                            <span>New Prescription</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Content with Professional Stats -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        <!-- Professional Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="stats-card p-6">
                <div class="flex items-center">
                    <div class="stats-icon active">
                        <i class="fas fa-pills text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Active Prescriptions</p>
                        <p class="text-2xl font-bold text-gray-900">{{ active_prescriptions }}</p>
                    </div>
                </div>
            </div>

            <div class="stats-card p-6">
                <div class="flex items-center">
                    <div class="stats-icon expiring">
                        <i class="fas fa-exclamation-triangle text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Expiring Soon</p>
                        <p class="text-2xl font-bold text-gray-900">{{ expiring_prescriptions }}</p>
                    </div>
                </div>
            </div>

            <div class="stats-card p-6">
                <div class="flex items-center">
                    <div class="stats-icon completed">
                        <i class="fas fa-check-circle text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Completed</p>
                        <p class="text-2xl font-bold text-gray-900">{{ completed_prescriptions }}</p>
                    </div>
                </div>
            </div>

            <div class="stats-card p-6">
                <div class="flex items-center">
                    <div class="stats-icon discontinued">
                        <i class="fas fa-ban text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Discontinued</p>
                        <p class="text-2xl font-bold text-gray-900">{{ discontinued_prescriptions }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Government-Level Professional Search Controls -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <!-- Left: Enhanced Search Bar with QR Scanner -->
                <div class="flex-1 max-w-md">
                    <div class="relative">
                        <input type="text" id="prescription-search" name="search"
                               class="prescriptions-search-input w-full pl-10 pr-12 py-2.5"
                               placeholder="Search prescriptions by medication, patient, or use QR scanner...">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-500"></i>
                        </div>
                        <!-- QR Code Scanner inside search bar -->
                        <button type="button" id="qr-scanner-btn" class="prescriptions-search-qr-button absolute inset-y-0 right-0 pr-3 flex items-center" title="Scan QR Code">
                            <i class="fas fa-qrcode"></i>
                        </button>
                    </div>
                </div>

                <!-- Right: Filter Controls -->
                <div class="flex items-center space-x-3">
                    <!-- Status Filter -->
                    <select id="status-filter" name="status" class="prescriptions-filter-button">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="completed">Completed</option>
                        <option value="discontinued">Discontinued</option>
                        <option value="on_hold">On Hold</option>
                    </select>

                    <!-- Frequency Filter -->
                    <select id="frequency-filter" name="frequency" class="prescriptions-filter-button">
                        <option value="">All Frequencies</option>
                        <option value="once_daily">Once Daily</option>
                        <option value="twice_daily">Twice Daily</option>
                        <option value="three_times_daily">Three Times Daily</option>
                        <option value="as_needed">As Needed</option>
                    </select>

                    <!-- Date Filter -->
                    <input type="date" id="date-filter" class="prescriptions-filter-button" title="Start Date">
                </div>
            </div>

            <!-- Results Count -->
            <div class="mt-3 flex items-center justify-between">
                <div class="results-count text-sm text-gray-600 font-medium">
                    Loading prescriptions...
                </div>
                <div class="text-xs text-gray-500">
                    Professional Medication Management System
                </div>
            </div>
        </div>

        <!-- Professional Prescriptions Management -->
        <div class="prescriptions-table-card">
            <div class="prescriptions-table-header">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Healthcare Prescriptions Registry</h3>
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <span>{{ prescriptions.count }} prescriptions</span>
                    </div>
                </div>
            </div>

            <!-- Clean Prescriptions Table -->
            <div class="overflow-x-auto">
                <table class="w-full border-collapse border border-gray-300">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="border border-gray-300 px-4 py-2 text-left">Medication</th>
                            <th class="border border-gray-300 px-4 py-2 text-left">Patient</th>
                            <th class="border border-gray-300 px-4 py-2 text-center">Dosage</th>
                            <th class="border border-gray-300 px-4 py-2 text-center">Frequency</th>
                            <th class="border border-gray-300 px-4 py-2 text-center">Status</th>
                            <th class="border border-gray-300 px-4 py-2 text-center">Prescribed By</th>
                            <th class="border border-gray-300 px-4 py-2 text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for prescription in prescriptions %}
                            <tr class="hover:bg-gray-50">
                                <td class="border border-gray-300 px-4 py-2">
                                    <div class="font-semibold">{{ prescription.medication }}</div>
                                    <div class="text-xs text-gray-500">ID: {{ prescription.id }}</div>
                                </td>
                                <td class="border border-gray-300 px-4 py-2">
                                    <div class="font-medium">{{ prescription.medical_record.patient.full_name }}</div>
                                    <div class="text-xs text-gray-500">{{ prescription.medical_record.patient.zimhealth_id }}</div>
                                </td>
                                <td class="border border-gray-300 px-4 py-2 text-center">
                                    {{ prescription.dosage }}
                                </td>
                                <td class="border border-gray-300 px-4 py-2 text-center">
                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                                        {{ prescription.effective_frequency }}
                                    </span>
                                </td>
                                <td class="border border-gray-300 px-4 py-2 text-center">
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
                                        {{ prescription.get_status_display|default:"Active" }}
                                    </span>
                                </td>
                                <td class="border border-gray-300 px-4 py-2 text-center">
                                    <div class="font-medium">{{ prescription.medical_record.doctor_name }}</div>
                                    <div class="text-xs text-gray-500">{{ prescription.medical_record.facility_name }}</div>
                                </td>
                                <td class="border border-gray-300 px-4 py-2 text-center">
                                    <div class="flex justify-center space-x-2">
                                        <a href="{% url 'api:prescription_detail' prescription_id=prescription.id %}" 
                                           class="bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600">
                                            View
                                        </a>
                                        <a href="{% url 'api:prescription_edit' prescription_id=prescription.id %}" 
                                           class="bg-green-500 text-white px-2 py-1 rounded text-xs hover:bg-green-600">
                                            Edit
                                        </a>
                                        <button onclick="confirmDeletePrescription('{{ prescription.id }}', '{{ prescription.medical_record.patient.full_name }}', '{{ prescription.medication }}')" 
                                                class="bg-red-500 text-white px-2 py-1 rounded text-xs hover:bg-red-600">
                                            Delete
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {% empty %}
                            <tr>
                                <td colspan="7" class="border border-gray-300 px-4 py-8 text-center text-gray-500">
                                    No prescriptions found
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- AJAX Pagination Container -->
            <div class="pagination-container px-6 py-4 border-t border-gray-200" style="display: none;">
                <!-- Pagination will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>
<!-- QR Code Scanner Modal -->
<div id="qr-modal" class="qr-modal">
    <div class="qr-modal-content">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">QR Code Scanner</h3>
            <button id="close-qr-btn" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="mb-4">
            <video id="qr-video" autoplay playsinline></video>
        </div>
        <div class="text-center">
            <p class="text-sm text-gray-600 mb-2">Point your camera at a patient QR code</p>
            <div class="flex items-center justify-center space-x-2 text-xs text-gray-500">
                <i class="fas fa-qrcode"></i>
                <span>QR codes contain patient ZimHealth-ID for instant lookup</span>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<!-- jsQR Library for QR Code Detection -->
<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
<script src="{% static 'assets/js/prescriptions.js' %}?v=**********"></script>
{% endblock %}
