"""
Views for the main ZimHealth-ID project.
"""

from django.shortcuts import render, redirect


def landing_page(request):
    """
    Landing page view for ZimHealth-ID.

    If user is authenticated, redirect to dashboard.
    Otherwise, show the landing page.
    """
    if request.user.is_authenticated:
        return redirect("api:dashboard")

    return render(request, "landing.html")


def redirect_to_landing_or_dashboard(request):
    """
    Redirect root URL to appropriate page based on authentication status.
    """
    if request.user.is_authenticated:
        return redirect("api:dashboard")
    else:
        return redirect("landing")
