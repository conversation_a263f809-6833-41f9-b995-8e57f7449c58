# Generated by Django 5.2.4 on 2025-08-12 10:43

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Patient",
            fields=[
                (
                    "zimhealth_id",
                    models.Char<PERSON>ield(
                        help_text="Unique ZimHealth-ID identifier",
                        max_length=20,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("first_name", models.CharField(max_length=100)),
                ("last_name", models.Char<PERSON>ield(max_length=100)),
                (
                    "national_id",
                    models.CharField(
                        blank=True,
                        help_text="Zimbabwean National ID (optional)",
                        max_length=20,
                        null=True,
                        unique=True,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Enter a valid Zimbabwean National ID (e.g., 63-123456A12)",
                                regex="^\\d{2}-\\d{6,7}[A-Z]\\d{2}$",
                            )
                        ],
                    ),
                ),
                ("date_of_birth", models.DateField()),
                (
                    "gender",
                    models.Char<PERSON>ield(
                        choices=[("M", "Male"), ("F", "Female"), ("O", "Other")],
                        max_length=1,
                    ),
                ),
                (
                    "nationality",
                    models.CharField(blank=True, default="Zimbabwean", max_length=100),
                ),
                (
                    "marital_status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Single", "Single"),
                            ("Married", "Married"),
                            ("Divorced", "Divorced"),
                            ("Widowed", "Widowed"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "phone_number",
                    models.CharField(
                        max_length=15,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.",
                                regex="^\\+?1?\\d{9,15}$",
                            )
                        ],
                    ),
                ),
                ("email", models.EmailField(blank=True, max_length=254, null=True)),
                ("address", models.TextField()),
                (
                    "emergency_contact",
                    models.CharField(
                        max_length=15,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Emergency contact must be entered in the format: '+999999999'. Up to 15 digits allowed.",
                                regex="^\\+?1?\\d{9,15}$",
                            )
                        ],
                    ),
                ),
                (
                    "blood_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("A+", "A+"),
                            ("A-", "A-"),
                            ("B+", "B+"),
                            ("B-", "B-"),
                            ("AB+", "AB+"),
                            ("AB-", "AB-"),
                            ("O+", "O+"),
                            ("O-", "O-"),
                        ],
                        max_length=3,
                        null=True,
                    ),
                ),
                (
                    "allergies",
                    models.JSONField(
                        blank=True, default=list, help_text="List of patient allergies"
                    ),
                ),
                ("registration_date", models.DateTimeField(auto_now_add=True)),
                (
                    "qr_code",
                    models.ImageField(
                        blank=True,
                        help_text="QR code for quick patient identification",
                        null=True,
                        upload_to="qr_codes/",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "user",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="patient_profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Patient",
                "verbose_name_plural": "Patients",
                "ordering": ["-registration_date"],
            },
        ),
        migrations.CreateModel(
            name="MedicalRecord",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        help_text="Unique identifier for the medical record",
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "date",
                    models.DateTimeField(
                        default=django.utils.timezone.now,
                        help_text="Date and time of the medical consultation",
                    ),
                ),
                (
                    "facility_name",
                    models.CharField(
                        help_text="Name of the healthcare facility", max_length=200
                    ),
                ),
                (
                    "doctor_name",
                    models.CharField(
                        help_text="Name of the attending doctor", max_length=100
                    ),
                ),
                (
                    "diagnosis",
                    models.TextField(
                        help_text="Primary diagnosis and any secondary diagnoses"
                    ),
                ),
                (
                    "treatment",
                    models.TextField(help_text="Treatment provided or recommended"),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True, help_text="Additional notes from the consultation"
                    ),
                ),
                (
                    "temperature",
                    models.DecimalField(
                        blank=True,
                        decimal_places=1,
                        help_text="Body temperature in Celsius",
                        max_digits=4,
                        null=True,
                    ),
                ),
                (
                    "blood_pressure_systolic",
                    models.IntegerField(
                        blank=True,
                        help_text="Systolic blood pressure (mmHg)",
                        null=True,
                    ),
                ),
                (
                    "blood_pressure_diastolic",
                    models.IntegerField(
                        blank=True,
                        help_text="Diastolic blood pressure (mmHg)",
                        null=True,
                    ),
                ),
                (
                    "heart_rate",
                    models.IntegerField(
                        blank=True, help_text="Heart rate (beats per minute)", null=True
                    ),
                ),
                (
                    "weight",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Patient weight in kg",
                        max_digits=5,
                        null=True,
                    ),
                ),
                (
                    "height",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Patient height in cm",
                        max_digits=5,
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="Healthcare provider who created this record",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_medical_records",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="medical_records",
                        to="api.patient",
                    ),
                ),
            ],
            options={
                "verbose_name": "Medical Record",
                "verbose_name_plural": "Medical Records",
                "ordering": ["-date"],
            },
        ),
        migrations.CreateModel(
            name="Appointment",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        help_text="Unique identifier for the appointment",
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "doctor_name",
                    models.CharField(
                        help_text="Name of the doctor for the appointment",
                        max_length=100,
                    ),
                ),
                ("date", models.DateField(help_text="Date of the appointment")),
                ("time", models.TimeField(help_text="Time of the appointment")),
                (
                    "appointment_type",
                    models.CharField(
                        choices=[
                            ("consultation", "General Consultation"),
                            ("follow_up", "Follow-up"),
                            ("check_up", "Check-up"),
                            ("vaccination", "Vaccination"),
                            ("screening", "Screening"),
                            ("emergency", "Emergency"),
                            ("specialist", "Specialist Consultation"),
                            ("dental", "Dental"),
                            ("mental_health", "Mental Health"),
                            ("other", "Other"),
                        ],
                        default="consultation",
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("scheduled", "Scheduled"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                            ("no_show", "No Show"),
                            ("rescheduled", "Rescheduled"),
                        ],
                        default="scheduled",
                        max_length=15,
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("normal", "Normal"),
                            ("high", "High"),
                            ("urgent", "Urgent"),
                        ],
                        default="normal",
                        max_length=10,
                    ),
                ),
                (
                    "facility_name",
                    models.CharField(
                        help_text="Name of the healthcare facility", max_length=200
                    ),
                ),
                (
                    "department",
                    models.CharField(
                        blank=True, help_text="Department or specialty", max_length=100
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True, help_text="Additional notes about the appointment"
                    ),
                ),
                ("reason", models.TextField(help_text="Reason for the appointment")),
                (
                    "estimated_duration",
                    models.IntegerField(
                        default=30, help_text="Estimated duration in minutes"
                    ),
                ),
                (
                    "reminder_sent",
                    models.BooleanField(
                        default=False,
                        help_text="Whether reminder has been sent to patient",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("cancelled_at", models.DateTimeField(blank=True, null=True)),
                ("cancellation_reason", models.TextField(blank=True)),
                (
                    "cancelled_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="cancelled_appointments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who created this appointment",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_appointments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="appointments",
                        to="api.patient",
                    ),
                ),
            ],
            options={
                "verbose_name": "Appointment",
                "verbose_name_plural": "Appointments",
                "ordering": ["date", "time"],
            },
        ),
        migrations.CreateModel(
            name="Prescription",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        help_text="Unique identifier for the prescription",
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "medication",
                    models.CharField(
                        help_text="Name of the medication", max_length=200
                    ),
                ),
                (
                    "dosage",
                    models.CharField(
                        help_text="Dosage amount (e.g., 500mg, 2 tablets)",
                        max_length=100,
                    ),
                ),
                (
                    "frequency",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("once_daily", "Once Daily"),
                            ("twice_daily", "Twice Daily"),
                            ("three_times_daily", "Three Times Daily"),
                            ("four_times_daily", "Four Times Daily"),
                            ("every_4_hours", "Every 4 Hours"),
                            ("every_6_hours", "Every 6 Hours"),
                            ("every_8_hours", "Every 8 Hours"),
                            ("every_12_hours", "Every 12 Hours"),
                            ("as_needed", "As Needed"),
                            ("before_meals", "Before Meals"),
                            ("after_meals", "After Meals"),
                            ("at_bedtime", "At Bedtime"),
                            ("other", "Other"),
                        ],
                        help_text="How often to take the medication",
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "custom_frequency",
                    models.CharField(
                        blank=True,
                        help_text="Custom frequency description (e.g., '3 times per day for 5 days', 'Every 2 hours as needed')",
                        max_length=200,
                        null=True,
                    ),
                ),
                (
                    "duration",
                    models.CharField(
                        help_text="Duration of treatment (e.g., 7 days, 2 weeks)",
                        max_length=100,
                    ),
                ),
                (
                    "instructions",
                    models.TextField(
                        blank=True,
                        help_text="Special instructions for taking the medication",
                    ),
                ),
                (
                    "quantity_prescribed",
                    models.IntegerField(
                        blank=True, help_text="Total quantity prescribed", null=True
                    ),
                ),
                (
                    "refills_allowed",
                    models.IntegerField(
                        default=0, help_text="Number of refills allowed"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("completed", "Completed"),
                            ("discontinued", "Discontinued"),
                            ("on_hold", "On Hold"),
                        ],
                        default="active",
                        max_length=15,
                    ),
                ),
                (
                    "start_date",
                    models.DateField(
                        default=django.utils.timezone.now,
                        help_text="Date to start taking the medication",
                    ),
                ),
                (
                    "end_date",
                    models.DateField(
                        blank=True,
                        help_text="Date to stop taking the medication",
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "medical_record",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="prescriptions",
                        to="api.medicalrecord",
                    ),
                ),
                (
                    "prescribed_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="Healthcare provider who prescribed this medication",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="prescribed_medications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Prescription",
                "verbose_name_plural": "Prescriptions",
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddIndex(
            model_name="patient",
            index=models.Index(
                fields=["zimhealth_id"], name="api_patient_zimheal_61b5bb_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="patient",
            index=models.Index(
                fields=["national_id"], name="api_patient_nationa_c03ec3_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="patient",
            index=models.Index(
                fields=["phone_number"], name="api_patient_phone_n_8fa6be_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="patient",
            index=models.Index(
                fields=["last_name", "first_name"],
                name="api_patient_last_na_abc6dd_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="medicalrecord",
            index=models.Index(
                fields=["patient", "-date"], name="api_medical_patient_928305_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="medicalrecord",
            index=models.Index(
                fields=["facility_name"], name="api_medical_facilit_a83599_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="medicalrecord",
            index=models.Index(
                fields=["doctor_name"], name="api_medical_doctor__47a93c_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="medicalrecord",
            index=models.Index(fields=["-date"], name="api_medical_date_63912a_idx"),
        ),
        migrations.AddIndex(
            model_name="appointment",
            index=models.Index(
                fields=["patient", "date"], name="api_appoint_patient_eb23c7_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="appointment",
            index=models.Index(
                fields=["date", "time"], name="api_appoint_date_c273d7_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="appointment",
            index=models.Index(
                fields=["doctor_name"], name="api_appoint_doctor__827d67_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="appointment",
            index=models.Index(fields=["status"], name="api_appoint_status_323c16_idx"),
        ),
        migrations.AddIndex(
            model_name="appointment",
            index=models.Index(
                fields=["facility_name"], name="api_appoint_facilit_f78049_idx"
            ),
        ),
        migrations.AddConstraint(
            model_name="appointment",
            constraint=models.UniqueConstraint(
                condition=models.Q(("status__in", ["scheduled", "rescheduled"])),
                fields=("patient", "date", "time", "doctor_name"),
                name="unique_active_appointment",
            ),
        ),
        migrations.AddIndex(
            model_name="prescription",
            index=models.Index(
                fields=["medical_record", "-created_at"],
                name="api_prescri_medical_2f5f72_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="prescription",
            index=models.Index(
                fields=["medication"], name="api_prescri_medicat_38720e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="prescription",
            index=models.Index(fields=["status"], name="api_prescri_status_7ba0cb_idx"),
        ),
        migrations.AddIndex(
            model_name="prescription",
            index=models.Index(
                fields=["start_date"], name="api_prescri_start_d_13221d_idx"
            ),
        ),
    ]
