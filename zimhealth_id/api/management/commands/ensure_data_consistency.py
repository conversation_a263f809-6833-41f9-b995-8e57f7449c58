from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from api.models import Patient, MedicalRecord, Prescription, Appointment


class Command(BaseCommand):
    help = "Ensure data consistency across all models and fix relationship issues"

    def add_arguments(self, parser):
        parser.add_argument(
            "--fix",
            action="store_true",
            help="Actually fix the issues found (default is dry-run)",
        )
        parser.add_argument(
            "--verbose",
            action="store_true",
            help="Show detailed output",
        )

    def handle(self, *args, **options):
        fix_issues = options["fix"]
        verbose = options["verbose"]

        self.stdout.write(self.style.SUCCESS("Starting data consistency check..."))

        issues_found = 0
        issues_fixed = 0

        # Check and fix patient issues
        patient_issues, patient_fixes = self.check_patients(fix_issues, verbose)
        issues_found += patient_issues
        issues_fixed += patient_fixes

        # Check and fix appointment issues
        appointment_issues, appointment_fixes = self.check_appointments(
            fix_issues, verbose
        )
        issues_found += appointment_issues
        issues_fixed += appointment_fixes

        # Check and fix medical record issues
        record_issues, record_fixes = self.check_medical_records(fix_issues, verbose)
        issues_found += record_issues
        issues_fixed += record_fixes

        # Check and fix prescription issues
        prescription_issues, prescription_fixes = self.check_prescriptions(
            fix_issues, verbose
        )
        issues_found += prescription_issues
        issues_fixed += prescription_fixes

        # Summary
        self.stdout.write("\n" + "=" * 50)
        self.stdout.write(f"Data Consistency Check Complete")
        self.stdout.write(f"Issues found: {issues_found}")
        if fix_issues:
            self.stdout.write(f"Issues fixed: {issues_fixed}")
            self.stdout.write(
                self.style.SUCCESS("✓ All fixable issues have been resolved")
            )
        else:
            self.stdout.write(self.style.WARNING("⚠ Run with --fix to resolve issues"))
        self.stdout.write("=" * 50)

    def check_patients(self, fix_issues, verbose):
        """Check and fix patient-related issues"""
        issues_found = 0
        issues_fixed = 0

        self.stdout.write("\n📋 Checking Patients...")

        # Check for patients without QR codes
        patients_without_qr = Patient.objects.filter(qr_code__isnull=True)
        count = patients_without_qr.count()
        if count > 0:
            issues_found += count
            self.stdout.write(f"  ❌ {count} patients without QR codes")

            if fix_issues:
                for patient in patients_without_qr:
                    try:
                        patient.generate_qr_code()
                        patient.save(update_fields=["qr_code"])
                        issues_fixed += 1
                        if verbose:
                            self.stdout.write(
                                f"    ✓ Generated QR code for {patient.full_name}"
                            )
                    except Exception as e:
                        self.stdout.write(
                            f"    ❌ Failed to generate QR for {patient.full_name}: {e}"
                        )

        # Check for inactive patients with active appointments
        inactive_patients_with_appointments = Patient.objects.filter(
            is_active=False, appointments__status="scheduled"
        ).distinct()
        count = inactive_patients_with_appointments.count()
        if count > 0:
            issues_found += count
            self.stdout.write(
                f"  ❌ {count} inactive patients with scheduled appointments"
            )

            if fix_issues:
                for patient in inactive_patients_with_appointments:
                    # Cancel future appointments for inactive patients
                    future_appointments = patient.appointments.filter(
                        status="scheduled", date__gte=timezone.now().date()
                    )
                    cancelled_count = future_appointments.update(
                        status="cancelled",
                        cancelled_at=timezone.now(),
                        cancellation_reason="Patient deactivated",
                    )
                    issues_fixed += cancelled_count
                    if verbose:
                        self.stdout.write(
                            f"    ✓ Cancelled {cancelled_count} appointments for {patient.full_name}"
                        )

        if issues_found == 0:
            self.stdout.write("  ✅ All patients are consistent")

        return issues_found, issues_fixed

    def check_appointments(self, fix_issues, verbose):
        """Check and fix appointment-related issues"""
        issues_found = 0
        issues_fixed = 0

        self.stdout.write("\n📅 Checking Appointments...")

        # Check for orphaned appointments (patient doesn't exist)
        try:
            orphaned_appointments = Appointment.objects.filter(patient__isnull=True)
            count = orphaned_appointments.count()
            if count > 0:
                issues_found += count
                self.stdout.write(f"  ❌ {count} orphaned appointments (no patient)")

                if fix_issues:
                    deleted_count = orphaned_appointments.delete()[0]
                    issues_fixed += deleted_count
                    self.stdout.write(
                        f"    ✓ Deleted {deleted_count} orphaned appointments"
                    )
        except Exception as e:
            self.stdout.write(f"  ❌ Error checking orphaned appointments: {e}")

        # Check for appointments with inactive patients
        appointments_inactive_patients = Appointment.objects.filter(
            patient__is_active=False, status="scheduled"
        )
        count = appointments_inactive_patients.count()
        if count > 0:
            issues_found += count
            self.stdout.write(
                f"  ❌ {count} scheduled appointments for inactive patients"
            )

            if fix_issues:
                updated_count = appointments_inactive_patients.update(
                    status="cancelled",
                    cancelled_at=timezone.now(),
                    cancellation_reason="Patient deactivated",
                )
                issues_fixed += updated_count
                self.stdout.write(
                    f"    ✓ Cancelled {updated_count} appointments for inactive patients"
                )

        # Check for past appointments still marked as scheduled
        past_scheduled = Appointment.objects.filter(
            status="scheduled", date__lt=timezone.now().date()
        )
        count = past_scheduled.count()
        if count > 0:
            issues_found += count
            self.stdout.write(
                f"  ❌ {count} past appointments still marked as scheduled"
            )

            if fix_issues:
                updated_count = past_scheduled.update(status="no_show")
                issues_fixed += updated_count
                self.stdout.write(
                    f"    ✓ Updated {updated_count} past appointments to no_show"
                )

        if issues_found == 0:
            self.stdout.write("  ✅ All appointments are consistent")

        return issues_found, issues_fixed

    def check_medical_records(self, fix_issues, verbose):
        """Check and fix medical record issues"""
        issues_found = 0
        issues_fixed = 0

        self.stdout.write("\n🏥 Checking Medical Records...")

        # Check for orphaned medical records
        try:
            orphaned_records = MedicalRecord.objects.filter(patient__isnull=True)
            count = orphaned_records.count()
            if count > 0:
                issues_found += count
                self.stdout.write(f"  ❌ {count} orphaned medical records (no patient)")

                if fix_issues:
                    # Don't auto-delete medical records, just report
                    self.stdout.write(
                        f"    ⚠ Manual review required for orphaned medical records"
                    )
        except Exception as e:
            self.stdout.write(f"  ❌ Error checking orphaned medical records: {e}")

        # Check for medical records with inactive patients
        records_inactive_patients = MedicalRecord.objects.filter(
            patient__is_active=False
        )
        count = records_inactive_patients.count()
        if count > 0:
            self.stdout.write(
                f"  ℹ {count} medical records for inactive patients (this is normal)"
            )

        # Check for medical records without required fields
        incomplete_records = (
            MedicalRecord.objects.filter(diagnosis__isnull=True)
            | MedicalRecord.objects.filter(diagnosis="")
            | MedicalRecord.objects.filter(treatment__isnull=True)
            | MedicalRecord.objects.filter(treatment="")
        )
        count = incomplete_records.count()
        if count > 0:
            issues_found += count
            self.stdout.write(
                f"  ❌ {count} medical records with missing diagnosis or treatment"
            )
            # Don't auto-fix these as they require medical input

        if issues_found == 0:
            self.stdout.write("  ✅ All medical records are consistent")

        return issues_found, issues_fixed

    def check_prescriptions(self, fix_issues, verbose):
        """Check and fix prescription issues"""
        issues_found = 0
        issues_fixed = 0

        self.stdout.write("\n💊 Checking Prescriptions...")

        # Check for orphaned prescriptions
        try:
            orphaned_prescriptions = Prescription.objects.filter(
                medical_record__isnull=True
            )
            count = orphaned_prescriptions.count()
            if count > 0:
                issues_found += count
                self.stdout.write(
                    f"  ❌ {count} orphaned prescriptions (no medical record)"
                )

                if fix_issues:
                    deleted_count = orphaned_prescriptions.delete()[0]
                    issues_fixed += deleted_count
                    self.stdout.write(
                        f"    ✓ Deleted {deleted_count} orphaned prescriptions"
                    )
        except Exception as e:
            self.stdout.write(f"  ❌ Error checking orphaned prescriptions: {e}")

        # Check for prescriptions with inactive patients
        prescriptions_inactive_patients = Prescription.objects.filter(
            medical_record__patient__is_active=False, status="active"
        )
        count = prescriptions_inactive_patients.count()
        if count > 0:
            issues_found += count
            self.stdout.write(
                f"  ❌ {count} active prescriptions for inactive patients"
            )

            if fix_issues:
                updated_count = prescriptions_inactive_patients.update(
                    status="discontinued"
                )
                issues_fixed += updated_count
                self.stdout.write(
                    f"    ✓ Discontinued {updated_count} prescriptions for inactive patients"
                )

        # Check for expired prescriptions still marked as active
        expired_prescriptions = Prescription.objects.filter(
            status="active", end_date__lt=timezone.now().date()
        )
        count = expired_prescriptions.count()
        if count > 0:
            issues_found += count
            self.stdout.write(
                f"  ❌ {count} expired prescriptions still marked as active"
            )

            if fix_issues:
                updated_count = expired_prescriptions.update(status="completed")
                issues_fixed += updated_count
                self.stdout.write(
                    f"    ✓ Marked {updated_count} expired prescriptions as completed"
                )

        if issues_found == 0:
            self.stdout.write("  ✅ All prescriptions are consistent")

        return issues_found, issues_fixed
