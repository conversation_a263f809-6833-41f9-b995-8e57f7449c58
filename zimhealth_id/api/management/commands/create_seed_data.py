import random
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from api.models import Patient, MedicalRecord, Prescription, Appointment
from zhid_auth.models import UserProfile


class Command(BaseCommand):
    help = "Create comprehensive seed data for ZimHealth-ID application"

    def add_arguments(self, parser):
        parser.add_argument(
            "--patients", type=int, default=20, help="Number of patients to create"
        )
        parser.add_argument(
            "--records", type=int, default=3, help="Average medical records per patient"
        )
        parser.add_argument(
            "--appointments",
            type=int,
            default=2,
            help="Average appointments per patient",
        )

    def handle(self, *args, **options):
        self.stdout.write("Creating seed data...")

        # Create healthcare providers
        providers = self.create_healthcare_providers()

        # Create patients
        patients = self.create_patients(options["patients"])

        # Create medical records with prescriptions
        self.create_medical_records(patients, providers, options["records"])

        # Create appointments
        self.create_appointments(patients, providers, options["appointments"])

        self.stdout.write(self.style.SUCCESS(f"Successfully created seed data:"))
        self.stdout.write(f"- {len(providers)} healthcare providers")
        self.stdout.write(f"- {len(patients)} patients")
        self.stdout.write(f"- {MedicalRecord.objects.count()} medical records")
        self.stdout.write(f"- {Prescription.objects.count()} prescriptions")
        self.stdout.write(f"- {Appointment.objects.count()} appointments")

    def create_healthcare_providers(self):
        providers_data = [
            {
                "username": "dr.moyo",
                "first_name": "Tendai",
                "last_name": "Moyo",
                "email": "<EMAIL>",
            },
            {
                "username": "dr.chikwanha",
                "first_name": "Chipo",
                "last_name": "Chikwanha",
                "email": "<EMAIL>",
            },
            {
                "username": "dr.mukamuri",
                "first_name": "Farai",
                "last_name": "Mukamuri",
                "email": "<EMAIL>",
            },
            {
                "username": "dr.sibanda",
                "first_name": "Nomsa",
                "last_name": "Sibanda",
                "email": "<EMAIL>",
            },
            {
                "username": "dr.nyoni",
                "first_name": "Blessing",
                "last_name": "Nyoni",
                "email": "<EMAIL>",
            },
        ]

        providers = []
        for data in providers_data:
            user, created = User.objects.get_or_create(
                username=data["username"],
                defaults={
                    "first_name": data["first_name"],
                    "last_name": data["last_name"],
                    "email": data["email"],
                    "is_staff": True,
                },
            )
            if created:
                user.set_password("healthcare123")
                user.save()
                UserProfile.objects.create(
                    user=user,
                    phone_number=f"+263{random.randint(700000000, 799999999)}",
                    professional_role="doctor",
                    hospital_name=random.choice(
                        ["Parirenyatwa Hospital", "Harare Hospital", "Mpilo Hospital"]
                    ),
                )
            providers.append(user)

        return providers

    def create_patients(self, count):
        zimbabwean_names = [
            ("Tinashe", "Madziva"),
            ("Chipo", "Mutasa"),
            ("Farai", "Chigumba"),
            ("Nomsa", "Dube"),
            ("Blessing", "Ncube"),
            ("Tendai", "Mapfumo"),
            ("Rutendo", "Chidziva"),
            ("Tapiwa", "Mukamuri"),
            ("Chiedza", "Gumbo"),
            ("Takudzwa", "Moyo"),
            ("Vimbai", "Chikwanha"),
            ("Panashe", "Sibanda"),
            ("Tatenda", "Nyoni"),
            ("Chenai", "Mpofu"),
            ("Ropafadzo", "Mangwende"),
            ("Munyaradzi", "Chitando"),
            ("Tsitsi", "Makoni"),
            ("Tafadzwa", "Zimunya"),
            ("Ruvimbo", "Chivasa"),
            ("Simbarashe", "Mavhura"),
        ]

        blood_types = ["A+", "A-", "B+", "B-", "AB+", "AB-", "O+", "O-"]
        genders = ["Male", "Female"]
        provinces = [
            "Harare",
            "Bulawayo",
            "Manicaland",
            "Mashonaland Central",
            "Mashonaland East",
            "Mashonaland West",
            "Masvingo",
            "Matabeleland North",
            "Matabeleland South",
            "Midlands",
        ]

        patients = []
        for i in range(count):
            first_name, last_name = random.choice(zimbabwean_names)
            birth_date = datetime.now().date() - timedelta(
                days=random.randint(365 * 18, 365 * 80)
            )

            patient = Patient.objects.create(
                first_name=first_name,
                last_name=last_name,
                national_id=f"{random.randint(10, 99)}-{random.randint(100000, 999999)}{chr(random.randint(65, 90))}{random.randint(10, 99)}",
                date_of_birth=birth_date,
                gender=random.choice(["M", "F"]),
                phone_number=f"+263{random.randint(700000000, 799999999)}",
                address=f'{random.randint(1, 999)} {random.choice(["Main", "Church", "Market", "Independence"])} Street, {random.choice(provinces)}',
                blood_type=random.choice(blood_types),
                allergies=[
                    random.choice(
                        [
                            "None",
                            "Penicillin",
                            "Peanuts",
                            "Shellfish",
                            "Latex",
                            "Dust mites",
                        ]
                    )
                ],
                emergency_contact=f"+263{random.randint(700000000, 799999999)}",
                nationality="Zimbabwean",
                marital_status=random.choice(
                    ["Single", "Married", "Divorced", "Widowed"]
                ),
            )
            patients.append(patient)

        return patients

    def create_medical_records(self, patients, providers, avg_records):
        diagnoses = [
            "Hypertension",
            "Diabetes Type 2",
            "Malaria",
            "Upper Respiratory Infection",
            "Gastroenteritis",
            "Pneumonia",
            "Tuberculosis",
            "HIV/AIDS",
            "Asthma",
            "Arthritis",
            "Migraine",
            "Depression",
            "Anxiety",
            "Back Pain",
            "Skin Infection",
        ]

        treatments = [
            "Medication prescribed",
            "Rest and fluids",
            "Physical therapy recommended",
            "Follow-up in 2 weeks",
            "Lifestyle changes advised",
            "Referral to specialist",
            "Laboratory tests ordered",
            "X-ray examination",
            "Blood pressure monitoring",
        ]

        facilities = [
            "Parirenyatwa Hospital",
            "Harare Hospital",
            "Mpilo Hospital",
            "United Bulawayo Hospitals",
            "Chitungwiza Hospital",
            "Mutare General Hospital",
            "Gweru General Hospital",
            "Masvingo General Hospital",
            "Bindura Hospital",
            "Kariba Hospital",
        ]

        medications = [
            ("Paracetamol", "500mg", "Twice daily"),
            ("Amoxicillin", "250mg", "Three times daily"),
            ("Metformin", "500mg", "Twice daily"),
            ("Lisinopril", "10mg", "Once daily"),
            ("Aspirin", "75mg", "Once daily"),
            ("Ibuprofen", "400mg", "As needed"),
            ("Omeprazole", "20mg", "Once daily"),
            ("Salbutamol", "100mcg", "As needed"),
            ("Atenolol", "50mg", "Once daily"),
            ("Simvastatin", "20mg", "Once daily"),
        ]

        for patient in patients:
            num_records = random.randint(1, avg_records * 2)

            for _ in range(num_records):
                visit_date = timezone.now() - timedelta(days=random.randint(1, 365))

                record = MedicalRecord.objects.create(
                    patient=patient,
                    date=visit_date,
                    doctor_name=f"Dr. {random.choice(providers).first_name} {random.choice(providers).last_name}",
                    facility_name=random.choice(facilities),
                    diagnosis=random.choice(diagnoses),
                    treatment=random.choice(treatments),
                    notes=f"Patient presented with symptoms. {random.choice(treatments)} Condition stable.",
                    temperature=round(random.uniform(36.0, 39.5), 1),
                    blood_pressure_systolic=random.randint(90, 180),
                    blood_pressure_diastolic=random.randint(60, 120),
                    heart_rate=random.randint(60, 120),
                    weight=round(random.uniform(45.0, 120.0), 1),
                    height=round(random.uniform(150.0, 190.0), 1),
                    created_by=random.choice(providers),
                )

                # Create prescriptions for some records
                if random.choice([True, False, True]):  # 66% chance
                    num_prescriptions = random.randint(1, 3)
                    for _ in range(num_prescriptions):
                        medication, dosage, frequency = random.choice(medications)

                        Prescription.objects.create(
                            medical_record=record,
                            medication=medication,
                            dosage=dosage,
                            frequency=random.choice(
                                ["once_daily", "twice_daily", "three_times_daily"]
                            ),
                            duration=f"{random.randint(5, 30)} days",
                            start_date=visit_date.date(),
                            end_date=(
                                visit_date + timedelta(days=random.randint(5, 30))
                            ).date(),
                            quantity_prescribed=random.randint(10, 60),
                            refills_allowed=random.randint(0, 3),
                            status=random.choice(
                                ["active", "completed", "discontinued"]
                            ),
                            instructions=f"Take {frequency} with food. Complete full course.",
                            prescribed_by=random.choice(providers),
                        )

    def create_appointments(self, patients, providers, avg_appointments):
        appointment_types = [
            "Consultation",
            "Follow-up",
            "Check-up",
            "Screening",
            "Emergency",
        ]
        statuses = ["Scheduled", "Completed", "Cancelled", "No-show"]
        priorities = ["Low", "Medium", "High", "Urgent"]

        facilities = [
            "Parirenyatwa Hospital",
            "Harare Hospital",
            "Mpilo Hospital",
            "United Bulawayo Hospitals",
            "Chitungwiza Hospital",
            "Mutare General Hospital",
            "Gweru General Hospital",
        ]

        for patient in patients:
            num_appointments = random.randint(1, avg_appointments * 2)

            for _ in range(num_appointments):
                # Mix of past and future appointments
                if random.choice([True, False]):
                    # Past appointment
                    appointment_date = timezone.now() - timedelta(
                        days=random.randint(1, 180)
                    )
                    status = random.choice(["Completed", "Cancelled", "No-show"])
                else:
                    # Future appointment
                    appointment_date = timezone.now() + timedelta(
                        days=random.randint(1, 90)
                    )
                    status = "Scheduled"

                # Random time between 8 AM and 5 PM
                hour = random.randint(8, 17)
                minute = random.choice([0, 15, 30, 45])
                appointment_time = appointment_date.replace(
                    hour=hour, minute=minute, second=0, microsecond=0
                )

                Appointment.objects.create(
                    patient=patient,
                    doctor_name=f"Dr. {random.choice(providers).first_name} {random.choice(providers).last_name}",
                    date=appointment_time.date(),
                    time=appointment_time.time(),
                    appointment_type=random.choice(
                        ["consultation", "follow_up", "check_up", "screening"]
                    ),
                    status=status.lower(),
                    priority=random.choice(["low", "normal", "high", "urgent"]),
                    facility_name=random.choice(facilities),
                    reason=f'{random.choice(appointment_types)} for {random.choice(["routine check", "follow-up care", "symptom evaluation", "preventive care"])}',
                    notes=f'Patient scheduled for appointment. {"Completed successfully." if status == "completed" else "Awaiting appointment." if status == "scheduled" else "Appointment not attended."}',
                    created_by=random.choice(providers),
                )
