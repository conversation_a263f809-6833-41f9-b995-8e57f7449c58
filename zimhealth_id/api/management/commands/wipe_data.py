from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from api.models import Patient, MedicalRecord, Prescription, Appointment
from zhid_auth.models import UserProfile


class Command(BaseCommand):
    help = "Wipe all data from the database for testing"

    def handle(self, *args, **options):
        self.stdout.write(self.style.WARNING("Wiping all data from database..."))

        # Delete all records
        Prescription.objects.all().delete()
        MedicalRecord.objects.all().delete()
        Appointment.objects.all().delete()
        Patient.objects.all().delete()
        UserProfile.objects.all().delete()
        User.objects.filter(is_superuser=False).delete()

        self.stdout.write(
            self.style.SUCCESS("Successfully wiped all data from database")
        )
        self.stdout.write(self.style.SUCCESS("Database is now clean for testing"))
