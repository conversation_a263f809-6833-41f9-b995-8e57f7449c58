from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.models import User
from .models import UserProfile, EmailVerificationToken, LoginAttempt


class UserProfileInline(admin.StackedInline):
    """Inline admin for user profile"""

    model = UserProfile
    can_delete = False
    verbose_name_plural = "Profile"
    fields = (
        "phone_number",
        "date_of_birth",
        "address",
        "bio",
        "avatar",
        "is_email_verified",
    )


class CustomUserAdmin(UserAdmin):
    """Custom user admin with profile inline"""

    inlines = (UserProfileInline,)
    list_display = (
        "username",
        "email",
        "first_name",
        "last_name",
        "is_staff",
        "is_active",
        "date_joined",
    )
    list_filter = ("is_staff", "is_active", "date_joined", "profile__is_email_verified")
    search_fields = ("username", "email", "first_name", "last_name")
    ordering = ("-date_joined",)

    def get_inline_instances(self, request, obj=None):
        if not obj:
            return list()
        return super().get_inline_instances(request, obj)


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """Admin for user profiles"""

    list_display = (
        "user",
        "phone_number",
        "is_email_verified",
        "created_at",
        "updated_at",
    )
    list_filter = ("is_email_verified", "created_at", "updated_at")
    search_fields = ("user__username", "user__email", "phone_number")
    readonly_fields = ("created_at", "updated_at")
    ordering = ("-created_at",)


@admin.register(EmailVerificationToken)
class EmailVerificationTokenAdmin(admin.ModelAdmin):
    """Admin for email verification tokens"""

    list_display = ("user", "token", "created_at", "expires_at", "is_used")
    list_filter = ("is_used", "created_at", "expires_at")
    search_fields = ("user__username", "user__email", "token")
    readonly_fields = ("token", "created_at")
    ordering = ("-created_at",)

    def has_add_permission(self, request):
        return False


@admin.register(LoginAttempt)
class LoginAttemptAdmin(admin.ModelAdmin):
    """Admin for login attempts"""

    list_display = ("email_attempted", "user", "ip_address", "success", "timestamp")
    list_filter = ("success", "timestamp")
    search_fields = ("email_attempted", "user__username", "ip_address")
    readonly_fields = (
        "user",
        "ip_address",
        "user_agent",
        "success",
        "timestamp",
        "email_attempted",
    )
    ordering = ("-timestamp",)

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False


# Unregister the default User admin and register our custom one
admin.site.unregister(User)
admin.site.register(User, CustomUserAdmin)
